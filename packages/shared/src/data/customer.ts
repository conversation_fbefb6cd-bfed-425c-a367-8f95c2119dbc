/*
 * @Date         : 2024-04-10 13:53:03
 * @Description  : 客户相关的数据
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export const roleList = [
  { label: "学生", value: "student" },
  { label: "家长", value: "parents" }
];

export const userPayList = [
  { label: "未付费", value: 1 },
  { label: "998已升单", value: 2 },
  { label: "998未升单", value: 3 },
  { label: "大会员", value: 4 },
  { label: "零售商品", value: 5 },
  { label: "体验课", value: 6 }
];

export const notNewQuestions: string[] = [
  "高一",
  "高二",
  "高三",
  "职一",
  "职二",
  "职三"
];

// 补差价分类
export const bigVipList = [
  { label: "全部", value: "" },
  { label: "小学", value: "小学" },
  { label: "初中", value: "初中" },
  { label: "高中", value: "高中" },
  { label: "全学段", value: "小学、初中、高中" }
];
// 补差价分类
export const newDiffStage = [
  { label: "全部", value: "" },
  { label: "小学", value: "小学" },
  { label: "初中", value: "初中" },
  { label: "高中", value: "高中" }
  // { label: "全学段", value: "全学段" }
];

// 体验赠送
export const subjectList = [
  { label: "小学数学", target: "vip#1-1" },
  { label: "小学思维培优", target: "bd772bfe-11c0-11ef-9f45-46decbf5b1ce" },
  { label: "小学实验", target: "vip#1-10" },
  { label: "小学语文", target: "vip#1-3" },
  { label: "小学英语", target: "vip#1-5" },

  { label: "初中数学", target: "vip#2-1" },
  { label: "初中物理", target: "vip#2-2" },
  { label: "初中化学", target: "vip#2-4" },
  { label: "初中语文", target: "vip#2-3" },
  { label: "初中英语", target: "vip#2-5" },
  { label: "初中生物", target: "vip#2-6" },
  { label: "初中地理", target: "vip#2-7" },

  { label: "高中数学", target: "vip#3-1" },
  { label: "高中物理", target: "vip#3-2" },
  { label: "高中化学", target: "vip#3-4" },
  { label: "高中生物", target: "vip#3-6" },
  { label: "高中英语", target: "vip#3-5" }
];

// 二级来源，需要显示的字段
export const sourceDetailMap = {
  lotteryDrawFission: "-抽奖"
};

// 线索来源
export const clueSource = {
  manual: "人工录入",
  mid_school_manual: "中学业务-人工",
  mid_school: "中学业务",
  custom_service_manual: "客服推送",
  transfer: "线索转移",
  primary_lab: "小学轻课实验组",
  WeCom: "企业微信",
  social_media: "新媒体",
  referral: "转介绍",
  tiyanying: "体验营",
  telesale_mp: "电销小程序",
  server_number: "洋葱服务号",
  parent: "家长端小程序",
  tiyan_upgrade: "体验升级策略",
  live: "视频号",
  research: "游学商品",
  luosi: "螺蛳教育",
  aladdin: "阿拉丁",
  aladdin_manual: "阿拉丁-人工录入",
  aladdin_referral: "阿拉丁-转介绍",
  aladdin_retry: "阿拉丁重试",
  repeated_exposure: "活码重复曝光",
  purchase: "订单成交",
  deal_transfer: "成交转移",
  phone_binding: "手机号换绑",
  mid_school_auto_transfer: "电话线索自动转移",
  referral_auto_transfer: "转介绍相关自动转移",
  building_blocks_goods_midschool: "千元品测试-中学业务",
  building_blocks_goods_manual: "千元品测试-定向分配",
  building_blocks_goods_wecom: "千元品测试-企业微信",
  family_other: "家庭其他线索",
  family_merge: "家庭合并",
  family_auto_transfer: "家庭自动流转",
  self_service_pool: "自助线索池",
  media998: "新媒体998"
};

// 线索来源分类
export const clueSourceTree = [
  {
    label: "中学业务",
    value: "mid_school"
  },
  {
    label: "人工录入",
    value: "manual"
  },
  {
    label: "客服推送",
    value: "custom_service_manual"
  },
  {
    label: "中学业务-人工",
    value: "mid_school_manual"
  },
  {
    label: "企业微信",
    value: "WeCom"
  },
  {
    label: "转介绍",
    value: "referral"
  },
  {
    label: "线索转移",
    value: "transfer"
  },
  {
    label: "自助线索池",
    value: "self_service_pool"
  },
  {
    label: "电话线索自动转移",
    value: "mid_school_auto_transfer"
  },
  {
    label: "转介绍小程序",
    value: "mini",
    children: [
      {
        label: "电销小程序",
        value: "telesale_mp"
      },
      {
        label: "洋葱服务号",
        value: "server_number"
      },
      {
        label: "家长端小程序",
        value: "parent"
      }
    ]
  },
  {
    label: "成交入库/流转",
    children: [
      {
        label: "订单成交",
        value: "purchase"
      },
      {
        label: "成交转移",
        value: "deal_transfer"
      }
    ]
  },
  {
    label: "家庭号",
    value: "family",
    children: [
      {
        label: "家庭其他线索",
        value: "family_other"
      },
      {
        label: "家庭合并",
        value: "family_merge"
      },
      {
        label: "家庭自动流转",
        value: "family_auto_transfer"
      }
    ]
  },
  {
    label: "其他",
    value: "other",
    children: [
      {
        label: "新媒体998",
        value: "media998"
      },
      {
        label: "手机号换绑",
        value: "phone_binding"
      },
      {
        label: "活码重复曝光",
        value: "repeated_exposure"
      },
      {
        label: "千元品-中学业务",
        value: "building_blocks_goods_midschool"
      },
      {
        label: "千元品-企业微信",
        value: "building_blocks_goods_wecom"
      },
      {
        label: "千元品-定向分配",
        value: "building_blocks_goods_manual"
      },
      {
        label: "转介绍相关自动转移",
        value: "referral_auto_transfer"
      },
      {
        label: "体验升级策略",
        value: "tiyan_upgrade"
      },
      {
        label: "视频号",
        value: "live"
      },
      {
        label: "游学商品",
        value: "research"
      },
      {
        label: "体验营",
        value: "tiyanying"
      },
      {
        label: "阿拉丁",
        value: "aladdin"
      },
      {
        label: "阿拉丁-人工录入",
        value: "aladdin_manual"
      },
      {
        label: "阿拉丁-转介绍",
        value: "aladdin_referral"
      },
      {
        label: "阿拉丁重试",
        value: "aladdin_retry"
      }
    ]
  }
];
