/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-24 12:24:08
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-23 10:40:45
 * @FilePath: /telesale-web_v2/packages/shared/src/businessHooks/priceDifference/utils.ts
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import { cloneDeep } from "lodash-es";

export const groupHandle = arr => {
  const priceDiffList = [
    {
      id: 99988888,
      goodName: "<span style='font-weight: bold'>全价链接</span>",
      type: "allLink",
      show: true,
      children: []
    },
    {
      id: 99999998,
      goodName: "<span style='font-weight: bold'>体验机链接</span>",
      type: "tiyanji",
      show: true,
      children: []
    }
  ];

  const list = cloneDeep(arr);
  list.forEach(item => {
    if (item.expMach) {
      priceDiffList[1].children.push(item);
    } else {
      priceDiffList[0].children.push(item);
    }
  });

  return priceDiffList.filter(item => item.children.length > 0);
};

export const diffAmountList = [
  { text: "¥ 7000及以上", value: "7000" },
  { text: "¥ 6000-6999", value: "6000-7000" },
  { text: "¥ 5000-5999", value: "5000-6000" },
  { text: "¥ 4000-4999", value: "4000-5000" },
  { text: "¥ 3000-3999", value: "3000-4000" },
  { text: "¥ 1-2999", value: "1-3000" }
];

export const deductCategoryList = [
  { label: "单笔最高", value: "highestOrder" },
  { label: "常规抵扣", value: "common" },
  { label: "体验机补差", value: "tiyanji" },
  { label: "大会员补差", value: "bigVip" },
  { label: "不能补差	", value: "none" }
];
