import { get<PERSON>abel } from "../../utils/index";

//根据数组中对象的某一个属性值进行排序从大到小
export function compareMax(prop) {
  return (a, b) => b[prop] - a[prop];
}

export const fromList = [
  {
    text: "电销",
    label: "电销",
    value: "telesale",
    auth: "telesale_admin_exclusive_qrcode_active"
  },
  {
    text: "体验营",
    label: "体验营",
    value: "tiyanying",
    auth: "telesale_admin_activeQrCode_tiyanying"
  },
  {
    text: "奥德赛",
    label: "奥德赛",
    value: "aodesai",
    auth: "telesale_admin_activeQrCode_aodesai"
  },
  {
    text: "本地化",
    label: "本地化",
    value: "localization",
    auth: "telesale_admin_activeQrCode_localization"
  }
];

export const durationList = [
  {
    text: "1年",
    value: 1
  },
  {
    text: "2年",
    value: 2
  },
  {
    text: "3年",
    value: 3
  },
  {
    text: "4年",
    value: 4
  },
  {
    text: "5年",
    value: 5
  },
  {
    text: "6年",
    value: 6
  },
  {
    text: "9年",
    value: 9
  }
];

export const schoolYearList = [
  {
    text: "一年级",
    value: "一年级"
  },
  {
    text: "二年级",
    value: "二年级"
  },
  {
    text: "三年级",
    value: "三年级"
  },
  {
    text: "四年级",
    value: "四年级"
  },
  {
    text: "五年级",
    value: "五年级"
  },
  {
    text: "六年级",
    value: "六年级"
  },
  {
    text: "七年级",
    value: "七年级"
  },
  {
    text: "八年级",
    value: "八年级"
  },
  {
    text: "九年级",
    value: "九年级"
  },
  {
    text: "高一",
    value: "高一"
  },
  {
    text: "高二",
    value: "高二"
  },
  {
    text: "高三",
    value: "高三"
  },
  {
    text: "职一",
    value: "职一"
  },
  {
    text: "职二",
    value: "职二"
  },
  {
    text: "职三",
    value: "职三"
  }
];

export const newSchoolYearList = [
  {
    id: 1,
    text: "小学",
    value: "1",
    durationList: [
      {
        text: "6年",
        value: 6
      },
      {
        text: "9年",
        value: 9
      }
    ]
  },
  {
    id: 2,
    text: "七年级",
    value: 7,
    durationList: [
      {
        text: "1年",
        value: 1
      }
      // {
      //   text: "3+1年",
      //   value: 4
      // },
      // {
      //   text: "6年",
      //   value: 6
      // }
    ]
  },
  {
    id: 3,
    text: "八年级",
    value: 8,
    durationList: [
      {
        text: "1年",
        value: 1
      },
      {
        text: "2年",
        value: 2
      }
      // {
      //   text: "6年",
      //   value: 6
      // }
    ]
  },
  {
    id: 4,
    text: "九年级",
    value: 9,
    durationList: [
      {
        text: "1年",
        value: 1
      }
      // {
      //   text: "4年",
      //   value: 4
      // }
    ]
  },
  {
    id: 5,
    text: "高中",
    value: "3",
    durationList: [
      {
        text: "4年",
        value: 4
      },
      {
        text: "25年6月30日到期",
        value: "timing-1"
      }
    ]
  }
];

export const ipadGoods = [
  {
    label: "S30",
    value: "S30"
  },
  {
    label: "Q20",
    value: "S20"
  }
];

export const ipadNoneMap = [
  {
    label: "无平板",
    value: "none",
    url: "",
    amount: ""
  }
];

// const notGood = ["小初大会员-4年", "小初大会员-5年", "小初高大会员-4年"];

export const getStageDuration = (value: string) => {
  return (
    newSchoolYearList.find(item => item.value === value)?.durationList || []
  );
};

export const getStageGood = (list, cname) => {
  // const currentTime = new Date().getTime();
  // const startTime = new Date("2024/05/31 00:00:00").getTime();
  // const endTime = new Date("2024/05/31 23:59:59").getTime();

  const data = list.find(item => item.cname === cname)?.goodList || [];
  // if (currentTime >= startTime && currentTime <= endTime) {
  //   if (cname === "high" || cname === "middle") return [];
  //   return data?.filter(item => !notGood.includes(item.name));
  // }
  return data;
};

export const getDuration = (type: string) => {
  if (type !== "high") return durationList;
  const arr = [];
  for (let index = 3; index < 13; index++) {
    arr.push({
      text: index + "年",
      value: index
    });
  }
  return arr;
};

export const getNewDuration = (type: string) => {
  let list: any = [];
  if (type === "common") {
    for (let index = 1; index < 13; index++) {
      list.push({
        text: index + "年",
        value: index
      });
    }
  }
  if (type === "high") {
    list = [
      { text: "3年", value: 3 },
      { text: "4年", value: 4 },
      { text: "24年9月到期", value: "timing-1" },
      { text: "25年9月到期", value: "timing-2" }
    ];
  }
  return list;
};

export const repurchaseTypeObj = {
  activity: "活动续购",
  commonXugou: "普通续购",
  primary: "小学特价平板续购",
  highActivity: "续购高考版大会员",
  highPrimary: "高考版小学续购"
};

const timingList = [
  { label: "24年9月到期", value: 1 },
  { label: "25年9月到期", value: 2 }
];

const pushTypeObj = {
  link: "专属链接",
  link_v2: "专属链接",
  // difference_v3: "补差价",
  difference_v4: "补差价",
  difference_v6: "补差价",
  blocksGood: "会场链接",
  repurchase: "续购",
  linkPad: "加购平板",
  zhufengPre2024: "省钱卡"
};

const stageList = [
  { label: "小学", value: 1 },
  { label: "初中", value: 2 },
  { label: "高中", value: 3 }
];

export const getPushType = (row: any) => {
  if (row.pushType === "link" && row.isNewExclusiveLink) {
    return "会场链接";
  }
  return pushTypeObj[row.pushType];
};

export const setAddContentName = async (
  list: any[],
  getConfigApi: Function
) => {
  const hasContent = list.some(item => item.addContent?.length);
  if (hasContent) {
    const res = await getConfigApi();
    const configList = [];
    res.data.data.forEach(item => {
      item.data.forEach(config => {
        if (config.addContent?.length) {
          configList.push(...config.addContent);
        }
      });
    });
    console.log(configList);
    list.forEach(item => {
      const contentId = item.addContent?.[0];
      const config = configList.find(config => config.label === contentId);
      item.addContentName = config?.name;
    });
  }
};

export const getCouresName = (row: any) => {
  let name = "";
  if (row.pushType === "link" || row.pushType === "link_v2") {
    if (!row.isNewExclusiveLink) {
      name = row.name;
    } else {
      if (row.stage) {
        name = `${row.name ? row.name : ""}${
          row.exchange === "none" || !row.exchange
            ? "-无平板"
            : `+${row.exchange}`
        }`;
      } else {
        if (row.vipType === "common") {
          if (row.exchange === "s30") {
            name =
              "普通版-换购平板S30" +
              `${row.schoolYear ? "-" + row.schoolYear : ""}` +
              "-" +
              row.duration +
              "年";
          } else {
            name =
              "普通版" +
              `${row.schoolYear ? "-" + row.schoolYear : ""}` +
              "-" +
              row.duration +
              "年";
          }
        } else {
          name =
            "高考版" +
            (row.schoolYear ? `-${row.schoolYear}` : "") +
            (row.duration
              ? `-${
                  row.goodType === "timing"
                    ? getLabel(row.duration, timingList)
                    : row.duration + "年"
                }`
              : "") +
            (row.exchange === "s30" ? "-换购平板S30" : "");
        }
      }
    }
  } else if (row.pushType === "repurchase") {
    name = repurchaseTypeObj[row.repurchaseType] + "-" + row.name;
  } else if (row.pushType === "linkPad") {
    name = row.exchange;
  } else {
    if (row.schoolYear) {
      if (row.pushType === "blocksGood" && row.schoolYear === "三年级") {
        name = row.name + "-一到" + row.schoolYear;
      } else {
        name = row.name + "-" + row.schoolYear;
      }

      if (row.addContentName) {
        if (row.addContent?.[0] === "notAdd") {
          name = name + `-${row.addContentName}`;
        } else {
          name = name + `-加购${row.addContentName}`;
        }
      }

      if (row.isPackGood) {
        name = name + "-打包购买";
      }
    } else {
      name = row.name;
    }
  }
  return name;
};

export const getDiffHideIds = (
  diffSettingData: any,
  isPush: boolean = false
) => {
  const hideIds = [];
  diffSettingData.data?.infos?.forEach(item => {
    let has = false;
    if (isPush && !item.showQR) {
      hideIds.push(item.skuGoodId);
      has = true;
    }

    if (!item.inRoot && !has) {
      if (item.visibleToPart && !item.inGroups) {
        hideIds.push(item.skuGoodId);
      }
    }
  });
  return hideIds;
};
