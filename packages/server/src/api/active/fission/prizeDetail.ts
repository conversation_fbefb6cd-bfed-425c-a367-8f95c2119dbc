/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-05 10:28:23
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-06-05 19:11:12
 * @FilePath: /telesale-web_v2/packages/server/src/api/active/fission/prizeDetail.ts
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import getHttp from "../../../utils/http/ceateAixos";
const http = getHttp();

// 虚拟奖品发放接口类型定义
export interface VirtualRewardsDeliveryReqBody {
  // 根据API文档，这个接口的请求体为空
}

export interface VirtualRewardsDeliveryResBody {
  // 空响应体
}

export interface ListRewardsRecordAllReqBody {
  mobile?: string;
  rewardName?: string;
  deliverGoodsStatus?: number;
  rewardAtStart?: number;
  rewardAtEnd?: number;
  pageIndex?: number;
  pageSize?: number;
  rewardType?: number;
}

export interface RewardsRecord {
  id?: number;
  onionId?: string;
  mobile?: string;
  rewardType?: string;
  rewardName?: string;
  rewardAt?: number;
  deliverGoodsStatus?: string;
  activityName?: string;
  activityTime?: number[];
}

export interface ListRewardsRecordAllResBody {
  list: RewardsRecord[];
  total: number;
}

// 解析奖品明细表格接口类型定义
export interface ParseWinningRecordItem {
  userId: string;
  onionId: string;
  phone: string;
  rewardType: number;
  rewardName: string;
  rewardAt: number;
  activityName: string;
}

export type ParseWinningRecordReqBody = ParseWinningRecordItem[];

export interface ImportRewardsRecordResBody {
  // 空响应体
}

export interface ParseWinningRecordResBody {
  // 根据API文档，返回空对象
}

// 批量导入奖品记录接口类型定义
export interface ImportRewardRecord {
  userId: string;
  onionId?: string;
  mobile?: string;
  rewardType?: number;
  rewardName?: string;
  rewardAt?: number;
  deliverGoodsStatus?: number;
  activityName?: string;
}

export interface ImportRewardsRecordReqBody {
  records: ImportRewardRecord[];
}

/**
 * @description 奖品发放-仅虚拟奖品
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {VirtualRewardsDeliveryReqBody} data
 * @returns {Promise<VirtualRewardsDeliveryResBody>}
 */
export const virtualRewardsDeliveryApi = (
  data: VirtualRewardsDeliveryReqBody
) => {
  return http.request<VirtualRewardsDeliveryResBody>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/virtualRewardsDelivery`,
    {
      data
    }
  );
};

/**
 * @description 获取奖品记录列表（所有记录）
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ListRewardsRecordAllReqBody} data
 * @returns {Promise<ListRewardsRecordAllResBody>}
 */
export const listRewardsRecordAllApi = (data: ListRewardsRecordAllReqBody) => {
  return http.request<ListRewardsRecordAllResBody>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/listRewardsRecordAll`,
    {
      data
    }
  );
};

/**
 * @description 批量导入奖品记录
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ImportRewardsRecordReqBody} data
 * @returns {Promise<ImportRewardsRecordResBody>}
 */
export const importRewardsRecordApi = (data: ImportRewardsRecordReqBody) => {
  return http.request<ImportRewardsRecordResBody>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/importRewardsRecord`,
    {
      data
    }
  );
};

/**
 * @description 导出中奖明细
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ListRewardsRecordAllReqBody} data
 * @returns {Promise<any>}
 */
export const exportWinningRecordApi = (data: ListRewardsRecordAllReqBody) => {
  return http.request<any>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/exportWinningRecord`,
    {
      data
    },
    {
      responseType: "blob"
    }
  );
};

/**
 * @description 解析奖品明细表格
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ParseWinningRecordReqBody} data
 * @returns {Promise<ParseWinningRecordItem>}
 */
export const parseWinningRecordExcelApi = (data: FormData) => {
  return http.request<ParseWinningRecordItem>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/parseWinningRecordExcel`,
    {
      data
    }
  );
};
