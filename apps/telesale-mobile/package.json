{"name": "telesale-mobile", "version": "1.0.0", "license": "ISC", "scripts": {"dev": "vite --mode dev", "dev:mock": "vite --mode mock", "dev:force": "vite --mode dev --force", "build": "vite build --mode dev", "build:test": "vite build --mode test", "build:stage": "vite build --mode stage", "build:master": "vite build --mode master", "serve": "vite preview --mode prod", "test": "jest", "cz": "git add . && git cz", "preview": "vite preview"}, "dependencies": {"@telesale/shared": "workspace:*", "@telesale/server": "workspace:*"}, "lint-staged": {"*.{vue,js,ts}": "eslint --fix"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}