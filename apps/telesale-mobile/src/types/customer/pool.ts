export interface SearchForm {
  workerid?: string;
  phone?: string;
  onionid?: string;
  callCount?: number;
}

export interface CustomerList {
  id: number;
  infoUuid: string;
  workerid: number;
  source: string;
  userid: string;
  userExpire: string;
  lastExpire: number;
  phone: string;
  nickname: string;
  stage: string;
  onionid: string;
}

export interface CustomerRes {
  list: CustomerList[];
  total: number;
}

export interface ManualInputForm {
  phone?: string;
  note?: string;
  workerid?: string | number;
  type?: "phone" | "onionId";
  onionId?: string;
  voucher: string[];
}
