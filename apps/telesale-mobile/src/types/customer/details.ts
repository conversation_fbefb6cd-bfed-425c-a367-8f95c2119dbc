export interface SearchForm {
  infoUuid: string;
}

export interface TriggerEvent {
  practiceType: string;
  practiceScene: string;
  subjectId: number;
}

export interface CustomerInfo {
  id: number;
  familyId: string;
  createdAt: string;
  updatedAt: string;
  infoUuid: string;
  workerid: number;
  source: string;
  userid: string;
  usertype: number;
  isLowQuality: number;
  userExpire: number;
  lastExpire: number;
  topicId: string;
  videoid: string;
  phone: string;
  nickname: string;
  receiveTime: number;
  extend: string;
  firstDialDuration: number;
  firstValidDial: number;
  note: string;
  grade: string;
  stage: string;
  isVIP: number;
  authList: string;
  regTime: string;
  onionid: string;
  intention: number;
  department: number;
  sender: string;
  historyAmount: number;
  label: string;
  tag: string;
  orderNum: number;
  isExcellent: number;
  featureTags: number[];
  weComOpenId: string;
  groupId0: number;
  groupId1: number;
  groupId2: number;
  groupId3: number;
  groupId4: number;
  role: string;
  lastDial: number;
  sourceDetail: string;
  siteId: number;
  user_tags: any[];
  triggerEvent: TriggerEvent;
  knowledge?: string;
  hasThinking?: boolean;
  region?: string;
  openMethod?: string;
  important?: string;
  pushMsg?: string;
  lastActiveTime?: number;
}

export interface Province {
  code: string;
  name: string;
  level: number;
  state: string;
}

export interface City {
  code: string;
  name: string;
  level: number;
  state: string;
}

export interface District {
  code: string;
  name: string;
  level: number;
  state: string;
}

export interface RegionType {
  province: Province;
  city: City;
  district: District;
}

export interface Agent {
  discountedPrices?: any;
  settlementPrices?: any;
}

export interface CourseTime {}

export interface Present {
  qqCoin: number;
  textbook: number;
  dress: number;
  ycCoin: number;
}

export interface DiscountProductIdMap {
  0: string;
}

export interface AdditionalData {
  type: string;
  discountProductIdMap: DiscountProductIdMap;
  productId: string;
}

export interface ExtraParam {
  curriculumId: string;
}

export interface Param {
  quantity: number;
  stage: string;
  subject: string;
  addTime: number;
}

export interface Distributor {
  kind: string;
  params: Param;
}

export interface Sku {
  isEnabled: boolean;
  images?: any;
  name: string;
  distributor: Distributor;
  amount: number;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  id: string;
}

export interface SkuList {
  skuId: string;
  amount: number;
  extraParams: ExtraParam;
  sku: Sku;
}

export interface Good {
  agent: Agent;
  courseTime: CourseTime;
  stopLossAmount: number;
  paymentPlatform?: any;
  buyOnce: boolean;
  buyLimit?: any;
  status: string;
  groupList: string[];
  images: any[];
  isEnabled: boolean;
  isDeleted: boolean;
  orderImages: any[];
  presents: Present;
  recommended: boolean;
  promotionInfo?: any;
  name: string;
  description: string;
  amount: number;
  originalAmount: number;
  additionalData: AdditionalData;
  createdBy: string;
  note: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  __note_2020_03_11: string;
  __note_2020_03_11_Date: string;
  skuList: SkuList[];
  skuSize: number;
  link: string;
  _id: string;
}

export interface CreationWay {
  appTypeVersion: string;
  channel: string;
  os: string;
  productId: string;
}

export interface Extra {
  open_id: string;
  bank_type: string;
}

export interface Refund {
  object: string;
  url: string;
  has_more: boolean;
  data?: any;
}

export interface Metadata {}

export interface Wx_lite {
  appId: string;
  partnerId: string;
  prepayId: string;
  nonceStr: string;
  timeStamp: string;
  package: string;
  paySign: string;
  signType: string;
}

export interface Credential {
  object: string;
  wx_lite: Wx_lite;
}

export interface PaymentCredential {
  id: string;
  object: string;
  created: number;
  livemode: boolean;
  paid: boolean;
  refunded: boolean;
  reversed: boolean;
  app: string;
  channel: string;
  order_no: string;
  client_ip: string;
  amount?: any;
  amount_settle?: any;
  currency: string;
  subject: string;
  body: string;
  extra: Extra;
  time_paid: number;
  time_expire: number;
  time_settle?: any;
  transaction_no: string;
  refunds: Refund;
  amount_refunded: number;
  failure_code?: any;
  failure_msg?: any;
  metadata: Metadata;
  credential: Credential;
  description: string;
}

export interface OrederType {
  promotion?: any;
  _id: string;
  status: string;
  userId: string;
  amount: number;
  good: Good;
  creationWay: CreationWay;
  paymentPlatform: string;
  remarks: any[];
  refundInfoList: any[];
  createdAt: string;
  updatedAt: string;
  isTest: boolean;
  paymentCredentials: PaymentCredential;
  attribution: string;
  paidTime: string;
}

export interface Choice {
  body: string;
  correct: boolean;
  jump: number;
  videoId: string;
}

export interface Interaction {
  id: string;
  videoId: string;
  time: number;
  jump: number;
  choices: Choice[];
  skip: boolean;
  skipTip: string;
  actionType: string;
}

export interface Addresse {
  id: string;
  url: string;
  platform: string;
  format: string;
  clarity: string;
  md5: string;
}

export interface Addresse {
  clarity: string;
  format: string;
  md5: string;
  platform: string;
  url: string;
}

export interface Head {
  addresses: Addresse[];
  duration: number;
}

export interface KeyPoint {
  id: string;
  time: number;
  content: string;
  description: string;
}

export interface Video {
  id: string;
  name: string;
  thumbnail: string;
  description: string;
  order: number;
  type: string;
  url: string;
  interactions: Interaction[];
  clips: any[];
  difficulty: number;
  addresses: Addresse[];
  duration: string;
  titleTime: string;
  head: Head;
  rear?: any;
  oldTitleTime: string;
  v: string;
  subtitleId: string;
  subtitleUrl: string;
  thumbnails: string;
  thumbnailsInterval: number;
  keyPoints: KeyPoint[];
  startPosition: number;
}

export interface Addresse {
  id: string;
  url: string;
  platform: string;
  format: string;
  clarity: string;
  md5: string;
}

export interface Choice {
  body: string;
  correct: boolean;
  jump: number;
  videoId: string;
}

export interface Interaction {
  id: string;
  videoId: string;
  time: number;
  jump: number;
  choices: Choice[];
  skip: boolean;
  skipTip: string;
  actionType: string;
}

export interface KeyPoint {
  id: string;
  time: number;
  content: string;
  description: string;
}

export interface Addresse {
  clarity: string;
  createTime: string;
  format: string;
  id: string;
  md5: string;
  platform: string;
  size: number;
  url: string;
  videoId: string;
}

export interface Head {
  addresses: Addresse[];
  duration: number;
}

export interface VideoList {
  id: string;
  name: string;
  titleTime: number;
  finishTime: number;
  duration: number;
  thumbnail: string;
  addresses: Addresse[];
  interactions: Interaction[];
  difficulty: number;
  description: string;
  subtitleId: string;
  subtitleUrl: string;
  redirect: boolean;
  screenshots: any[];
  goals: string[];
  keyPoints: KeyPoint[];
  type: string;
  thumbnails: string;
  thumbnailsInterval: number;
  freeUrl: string;
  head: Head;
  rear?: any;
  oldTitleTime: number;
  v: string;
  startPosition: number;
}

export interface Topic {
  id: string;
  name: string;
  type: string;
  pay: boolean;
  keyPoint: boolean;
  coverImage: string;
  isFreeTime: boolean;
  description: string;
  video: VideoList;
  practices?: any;
  isQimen: boolean;
  contentList: any[];
  summaryImage: string;
  firstPublishAt: string;
}

export interface VideoRecordType {
  video: VideoList;
  topic: Topic;
  timestamp: number;
  publisherName: string;
  subjectName: string;
  semesterName: string;
  isFreeTime: boolean;
  pay: boolean;
}

export interface CallRecordType {
  id: string;
  createdAt: string;
  intention: string;
  note: string;
  notifyTime: number;
}

export interface LockRes {
  infoUuid: string[];
}
