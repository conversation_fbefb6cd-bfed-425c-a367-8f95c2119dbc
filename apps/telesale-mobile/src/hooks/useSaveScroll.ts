import { onBeforeRouteLeave } from 'vue-router'
import { Ref } from 'vue'

export default function(dom: Ref<HTMLElement>) {
  const scrollNum = ref<number>(0)
  const domRef = ref(document.body)

  onBeforeRouteLeave((to, from, next) => {  // 组件内路由守卫  离开组件前记录页面位置
    scrollNum.value = domRef.value.scrollTop
    next()
  })

  onActivated(() => {
    if (scrollNum.value != null && scrollNum.value > 0) {
      domRef.value.scrollTop = scrollNum.value
    }
  })

  watch(dom, newV => {
    domRef.value = dom.value
    console.log('thisss', newV)
  })
}
