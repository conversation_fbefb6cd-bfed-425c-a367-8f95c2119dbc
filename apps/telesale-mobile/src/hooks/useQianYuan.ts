/*
 * @Date         : 2024-11-14 17:09:57
 * @Description  : 千元品测试组
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { storeToRefs } from "pinia";
import { isExperimentGroupWorkerApi } from "@/api/common";
import { useUserStore } from "@/store/modules/user";
import { findPositionApi } from "@/api/user";
import { isStage } from "@telesale/shared/src/utils/business";

export const useQianYuan = () => {
  const { userMsg } = storeToRefs(useUserStore());
  const hasPermissions = ref<boolean>(false);
  const loading = ref<boolean>(false);
  const getHasPermissions = async () => {
    try {
      loading.value = true;
      const orgData: any = await findPositionApi({
        key: "id",
        value: userMsg.value.id + ""
      });
      const ids = orgData.data.pathList?.reduce((pre, cur) => {
        return pre.concat(cur.id?.split?.(","));
      }, []);
      const isALlData = [...new Set(ids)].some(item =>
        isStage.includes(Number(item))
      );
      if (isALlData) {
        hasPermissions.value = false;
      } else {
        const res = await isExperimentGroupWorkerApi({
          workerId: userMsg.value.id as number
        });
        hasPermissions.value = res.data.isExperimentGroupWorker;
      }
    } finally {
      loading.value = false;
    }
  };

  return {
    hasPermissions,
    loading,
    getHasPermissions
  };
};
