import { useAppStore } from "@/store/modules/app";
import { useRouter } from "vue-router";

/**
 * 回退，清除缓存的路由
 * @param number 回退的的路由层级
 * @param names 删除缓存的路由
 */
export const useBackRoute = () => {
  const router = useRouter();
  const back = (number: number, names: string[]) => {
    const { removeKeepAlive } = useAppStore();
    removeKeepAlive(names);
    router.go(number);
  };
  return {
    back
  };
};
