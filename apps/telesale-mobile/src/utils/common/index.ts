//将ISOS时间（以T分隔）转换为年月日时分秒 type:1
//将时间戳转换为年月日时分秒 type:2
//将UTC时间(以Z结尾)转换为年月日时分秒 type:3
export function timeChange(time: string | any, type: 1 | 2 | 3) {
  if (!time || time === "0001-01-01T00:00:00Z" || time === "0") {
    return "";
  }
  if (type === 1) {
    return time.replace(/T/g, " ").slice(0, 19);
  } else if (type === 2) {
    return new Date((Number(time) + 8 * 3600) * 1000)
      .toISOString()
      .replace(/T/g, " ")
      .replace(/\.[\d]{3}Z/, "");
  } else {
    return new Date(new Date(time).getTime() + 8 * 3600 * 1000)
      .toISOString()
      .replace(/T/g, " ")
      .replace(/\.[\d]{3}Z/, "");
  }
}

export function getLabel<T>(
  str: string | number | undefined | null | boolean,
  arr: T[],
  label = "label",
  value: string | number = "value"
) {
  if (!String(str)) return "";
  const newObj = arr.find(item => String(item[value]) === String(str));
  const returnValue = newObj?.[label] ?? "";
  return returnValue;
}

export const getArrayBufferBase64 = (buffer: ArrayBuffer) => {
  return new Promise<string>(resolve => {
    const arrayBufferView = new Uint8Array(buffer);
    const blob = new Blob([arrayBufferView], { type: "image/jpeg" });
    const fileReader = new FileReader();
    fileReader.onload = function (e) {
      const base64String = window.btoa(e.target?.result as string);
      resolve(`data:image/jpeg;base64,${base64String}`);
    };
    fileReader.readAsBinaryString(blob);
  });
};
