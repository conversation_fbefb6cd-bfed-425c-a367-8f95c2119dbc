import { Directive, createApp } from "vue";
import App from "./App.vue";

import router from "./router";
import pinia from "./store";
import VConsole from "vconsole";

import "@unocss/reset/normalize.css";
import "virtual:svg-icons-register";
import "virtual:uno.css";
import "vant/es/toast/style";
import "vant/es/dialog/style";
import { setToastDefaultOptions } from "vant";
import * as directives from "@/directives";

console.log("import.env", import.meta.env);
if (import.meta.env.VITE_ENV !== "master") {
  const vconsole = new VConsole();
}

setToastDefaultOptions("loading", { duration: 60000, forbidClick: true });

const app = createApp(App);
// 注册自定义指令
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

app.use(router);
app.use(pinia);
app.mount("#app");
