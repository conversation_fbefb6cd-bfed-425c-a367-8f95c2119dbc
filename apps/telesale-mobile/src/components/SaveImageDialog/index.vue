<script lang="ts" setup>
import { computed } from "vue";

const props = defineProps<{
  show: boolean;
  imgUrl: string;
}>();

const emits = defineEmits(["update:show"]);

const showValue = computed<boolean>({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});
</script>

<template>
  <div>
    <van-dialog
      v-model:show="showValue"
      title="长按图片保存到相册"
      confirmButtonText="关闭"
    >
      <div class="w-100% max-h-60vh overflow-y-auto text-center">
        <slot />
        <img class="w-100% h-100%" :src="props.imgUrl" />
      </div>
    </van-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-dialog__header) {
  padding: 20px 0;
}
</style>
