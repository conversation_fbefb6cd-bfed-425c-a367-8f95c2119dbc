<template>
  <van-field
    v-model="text"
    v-bind="$attrs"
    readonly
    border
    is-link
    @click="show = !show"
  />
  <van-popup :show="show" round position="bottom">
    <van-picker
      :columns="props.columns"
      :columns-field-names="{
        text: props.options.label,
        value: props.options.value
      }"
      @cancel="show = !show"
      @confirm="onConfirm"
    />
  </van-popup>
</template>

<script lang="ts" setup>
import { getLabel } from "@/utils/common";
import { ref, computed } from "vue";
const emits = defineEmits(["update:value", "confim"]);

const props = withDefaults(
  defineProps<{
    value: any;
    columns: any[];
    options?: {
      label: string;
      value: string;
    };
  }>(),
  {
    options: () => {
      return {
        label: "label",
        value: "value"
      };
    }
  }
);

let show = ref<boolean>(false);
let text = ref<string>("");
const showValue = computed({
  get() {
    return props.value;
  },
  set(val) {
    emits("update:value", val);
  }
});

const onConfirm = ({ selectedOptions }) => {
  if (!selectedOptions[0]) {
    show.value = false;
    return;
  }
  const { value, label } = props.options;
  text.value = selectedOptions[0][label];
  showValue.value = selectedOptions[0][value];
  show.value = !show.value;
  emits("confim", selectedOptions[0]);
};

watch(
  () => showValue.value,
  () => {
    init();
  }
);

const getText = () => {
  const current = props.columns.find(
    item => item[props.options.value] === props.value
  );
  const text = current?.[props.options.label] || "";
  return text;
};

const init = () => {
  text.value = getText();
};

init();
</script>
