<!--
 * @Date         : 2024-06-14 10:52:20
 * @Description  : 多选框组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<template>
  <van-field
    v-model="selectedValues"
    v-bind="$attrs"
    readonly
    border
    is-link
    @click="openPicker"
  />
  <van-popup :show="show" round position="bottom" :style="{ height: '40%' }">
    <div class="relative">
      <div class="flex justify-between p-40px sticky top-0 bg-white z-10000">
        <van-icon name="cross" @click="show = false" />
        <span class="finish" @click="submit">完成</span>
      </div>
      <van-checkbox-group v-model="checked">
        <van-cell-group inset>
          <van-cell
            v-for="(item, index) in list"
            clickable
            :key="item.value"
            :title="item.label"
            @click="toggle(index, item.value)"
          >
            <template #right-icon>
              <van-checkbox
                :name="item.value"
                :ref="el => (checkboxRefs[index] = el)"
                @click.stop
                :disabled="!checked.includes(item.value) && disabled"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
import { computed, onBeforeUpdate } from "vue";
import { getLabel } from "@/utils/common";
import { showToast } from "vant";
const emits = defineEmits(["update:value"]);

const props = withDefaults(
  defineProps<{
    value: any[];
    columns: any[];
    multipleLimit?: number;
    options?: {
      label: string;
      value: string;
    };
  }>(),
  {
    options: () => {
      return {
        label: "label",
        value: "value"
      };
    }
  }
);

const show = ref<boolean>(false);
const checkboxRefs = ref([]);
const text = ref<string>("");
const checked = ref([]);
const disabled = computed(() => {
  if (!props.multipleLimit) return false;
  return checked.value.length >= props.multipleLimit;
});

const list = computed(() => {
  const list = props.columns;
  list.forEach(item => {
    item.label = item[props.options.label];
    item.value = item[props.options.value];
  });
  return props.columns || [];
});

const selectedValues = ref<string>("");

const getValues = () => {
  const values = [];
  props.columns.forEach(item => {
    if (checked.value.includes(item[props.options.value]))
      values.push(item[props.options.label]);
  });
  return values.join("、");
};

const openPicker = () => {
  checked.value = props.value;
  show.value = true;
};

const toggle = (index: number, value: any) => {
  if (disabled.value && !checked.value.includes(value)) return;
  checkboxRefs.value[index].toggle();
};

const submit = () => {
  selectedValues.value = getValues();
  emits("update:value", checked.value);
  show.value = false;
};

onBeforeUpdate(() => {
  checkboxRefs.value = [];
});
</script>

<style lang="scss" scoped>
.finish {
  flex: 0 0 auto;
  color: $primary-color;
}
:deep(.van-field__control) {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}
</style>
