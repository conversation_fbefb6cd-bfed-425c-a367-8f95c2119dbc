<template>
  <div>
    <van-popup
      v-model:show="isShow"
      class="vanPopupRef"
      round
      position="bottom"
      :style="{ height: '80%' }"
    >
      <div class="p-2">
        <div class="flex justify-between p-20px top">
          <van-icon name="cross" @click="isShow = false" />
          <span v-if="currentName">当前选择-{{ currentName }}</span>
          <span v-else>{{ props.title }}</span>
          <span class="finish" @click="submit">完成</span>
        </div>
        <van-search
          v-model="searchVal"
          class="search"
          placeholder="请输入搜索关键词"
          shape="round"
          @update:model-value="filterSearch"
        />
        <VirtualScroll
          :itemHeight="41"
          :height="height"
          :data="filterData"
          :bench="20"
        >
          <template #default="{ item }">
            <div
              class="item flex justify-between p-20px"
              @click="changeVal(item)"
            >
              <span v-html="selectRed(item[props.options.label])" />
              <van-icon
                v-if="
                  props.options.value
                    ? item[props.options.value] == currentVal
                    : true
                "
                name="success"
                class="finish"
              />
            </div>
          </template>
        </VirtualScroll>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { debounce, cloneDeep } from "lodash-es";
import { useResizeObserver } from "@vueuse/core";
import VirtualScroll from "@/components/VirtualScroll/index.vue";
import { getLabel } from "@/utils/common";

interface Options {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    value: any;
    name: string;
    data: any[];
    show: boolean;
    title: string;
    options?: Options;
  }>(),
  {
    options: () => {
      return {
        label: "label",
        value: "id"
      };
    }
  }
);

const emits = defineEmits([
  "update:show",
  "update:value",
  "update:name",
  "filterChange",
  "submit"
]);

const isShow = computed({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});

const currentVal = ref<any>(props.value);
const currentName = ref<string>(props.name);
const searchVal = ref<string>("");
const height = ref<number>();
const filterData = ref(cloneDeep(props.data));

onMounted(() => {
  useResizeObserver(
    document.querySelector(".vanPopupRef") as HTMLElement,
    entries => {
      const entry = entries[0];
      const { height: domHeight } = entry.contentRect;
      const top = document.querySelector(".top")?.clientHeight || 0;
      const search = document.querySelector(".search")?.clientHeight || 0;
      height.value = domHeight - top - search - 20;
    }
  );
});

const filterSearch = debounce(val => {
  filterData.value = props.data.filter(item => {
    return item[props.options.label].includes(val);
  });
}, 500);

const changeVal = (row: any) => {
  const value = row[props.options.value || "id"];
  if (value === Number(currentVal.value)) {
    currentVal.value = undefined;
    currentName.value = "";
    return;
  }
  currentVal.value = value;
  currentName.value = getLabel(
    currentVal.value,
    props.data,
    props.options.label,
    props.options.value
  );
};

const selectRed = label => {
  return label.replace(
    new RegExp(searchVal.value, "g"),
    `<span style="color: red">${searchVal.value}</span>`
  );
};

const submit = () => {
  emits("update:value", currentVal.value ?? undefined);
  emits("update:name", currentName.value || "");
  emits("submit", currentVal.value);
  isShow.value = false;
};
</script>

<style lang="scss" scoped>
.finish {
  flex: 0 0 auto;
  color: $primary-color;
}
.item {
  border-bottom: 1px solid #9d9d9d;
}
</style>
