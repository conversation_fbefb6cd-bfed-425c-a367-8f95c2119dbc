<script lang="ts" setup generic="T extends { [x: string]: any }">
import type { ListInstance } from "vant";
import { onBeforeRouteLeave } from "vue-router";

const props = withDefaults(
  defineProps<{
    list: T[];
    loading: boolean;
    finished: boolean;
    onLoad: ((...args: any[]) => any) | undefined;
  }>(),
  {
    loading: false,
    finished: false
  }
);

const listRef = ref<ListInstance>();
const top = ref<number>(0);

onActivated(() => {
  const el = document.querySelector(".my-list") as HTMLElement;
  el?.scrollTo(0, top.value);
});

onBeforeRouteLeave(() => {
  top.value = document.querySelector(".my-list")?.scrollTop || 0;
});
</script>

<template>
  <div class="my-list">
    <van-list
      v-if="list.length > 0"
      ref="listRef"
      v-bind="$attrs"
      :loading="props.loading"
      :finished="finished"
      finished-text="没有更多了"
      error-text="请求失败，点击重新加载"
      @load="onLoad"
    >
      <div v-for="(item, index) in list" :key="index">
        <slot :data="item" />
      </div>
    </van-list>
    <template v-else>
      <van-empty image="search" description="暂无数据" />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.my-list {
  height: 100%;
  overflow: hidden auto;
}
</style>
