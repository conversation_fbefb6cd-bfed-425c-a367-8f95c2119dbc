import { RouteRecordRaw } from "vue-router";

const orderRouter: RouteRecordRaw[] = [
  {
    path: "/order/list",
    component: () => import("@/views/order/orderList/index.vue"),
    meta: {
      title: "订单列表",
      removeKeepAlive: ["orderSearch", "customerDetail"]
    }
  },
  {
    path: "/order/list/search",
    name: "orderSearch",
    component: () => import("@/views/order/orderList/pages/searchOrder.vue"),
    meta: {
      title: "订单搜索",
      keepAlive: true
    }
  },
  {
    path: "/order/appeal",
    component: () => import("@/views/order/appeal/index.vue"),
    meta: {
      title: "申诉列表",
      removeKeepAlive: ["appealSearch"]
    }
  },
  {
    path: "/order/appeal/search",
    name: "appealSearch",
    component: () => import("@/views/order/appeal/pages/searchAppeal.vue"),
    meta: {
      title: "申诉搜索",
      keepAlive: true
    }
  },
  {
    path: "/order/appeal/add",
    component: () => import("@/views/order/appeal/pages/addAppeal.vue"),
    meta: {
      title: "新增申诉"
    }
  }
];

export default orderRouter;
