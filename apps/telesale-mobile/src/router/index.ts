import { isEmpty } from "lodash-es";
import { storeToRefs } from "pinia";
import { createWebHistory, createRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import { useAppStore } from "@/store/modules/app";
import customerRouter from "./modules/customer";
import commonRouter from "./modules/common";
import orderRouter from "./modules/order";
import { getTokenApi } from "@/api/user";
import { showLoadingToast, closeToast, showToast, showDialog } from "vant";

const router = createRouter({
  history: createWebHistory("WH_CRM_v2/telesale-mobile"),
  routes: [...customerRouter, ...commonRouter, ...orderRouter]
});

function apiAuth() {
  return new Promise((reslove, reject) => {
    window.h5sdk.error(err => {
      console.log("h5sdk error:", JSON.stringify(err));
      reject(err);
    });
    // 通过ready接口确认环境准备就绪后才能调用API
    window.h5sdk.ready(() => {
      console.log("window.h5sdk.ready");
      // 调用JSAPI tt.requestAuthCode 获取 authorization code
      window.tt.requestAuthCode({
        appId: import.meta.env.VITE_FEISHU_ID,
        // 获取成功后的回调
        success({ code }) {
          getTokenApi({ code: code })
            .then(res => {
              localStorage.setItem("whcrmAuthorization", res.token);
              reslove(true);
            })
            .catch(err => {
              if (err.response.status === 400) {
                showDialog({
                  title: "提示",
                  message: "您暂未在本系统注册坐席，请联系您的主管！"
                }).then(() => {
                  window.tt.closeWindow();
                });
              }
              reject(err);
            });
        },
        // 获取失败后的回调
        fail(err) {
          console.log(`getAuthCode failed, err:`, JSON.stringify(err));
          showToast("飞书授权失败");
          reject(err);
        }
      });
    });
  });
}

const setKeepAlive = to => {
  const { removeKeepAlive = [], keepAlive } = to.meta || {};
  if ((removeKeepAlive as string[])?.length > 0 || keepAlive) {
    const useApp = useAppStore();
    keepAlive && useApp.setKeepAlive(to.name as string);
    removeKeepAlive && useApp.removeKeepAlive(removeKeepAlive as string[]);
  }
};

const getInfo = async (to, form) => {
  showLoadingToast("加载中...");
  const useUser = useUserStore();
  const { getAllAgentObj, getUserInfoAction, getAgentList, getUserMsg } =
    useUser;
  const { allAgentObj, userInfo, agentList, userMsg } = storeToRefs(useUser);
  try {
    if (isEmpty(userMsg.value)) {
      await getUserMsg();
    }
    if (isEmpty(userInfo.value)) {
      await getUserInfoAction();
    }
    if (isEmpty(allAgentObj.value)) {
      await getAllAgentObj();
    }
    if (isEmpty(agentList.value)) {
      await getAgentList();
    }
  } catch (err) {
    console.error(err);
  } finally {
    closeToast();
  }
  // 设置和删除keepalive
  setKeepAlive(to);
};

// 路由白名单
const whiteList = ["/error"];

router.beforeEach(async (to, form, next) => {
  if (whiteList.includes(to.path)) {
    next();
  }
  if (!window.h5sdk) {
    next({ path: "/error" });
  } else {
    const useUser = useUserStore();
    const { userMsg } = storeToRefs(useUser);
    if (isEmpty(userMsg.value)) {
      localStorage.removeItem("whcrmAuthorization");
    }
    const token = localStorage.getItem("whcrmAuthorization");
    if (token) {
      await getInfo(to, form);
      next();
    } else {
      if (to.path !== "/") {
        localStorage.removeItem("whcrmAuthorization");
        window.history.replaceState(
          null,
          "",
          `${location.origin}/WH_CRM_v2/telesale-mobile/`
        );
        window.history.go(0);
      }

      await apiAuth();
      await getInfo(to, form);
      next();
    }
  }
});

router.afterEach(to => {
  document.title = (to.meta.title as string) || "电销CRM";
});

export default router;
