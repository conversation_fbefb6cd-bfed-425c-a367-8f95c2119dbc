<script lang="ts" setup>
import { ref } from "vue";
import { cloneDeep } from "lodash-es";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useBackRoute } from "@/hooks/router/useBackRoute";
import MyUpload from "@/components/MyUpload/index.vue";
import {
  addAppealApi,
  AppealReq,
  getAppealExistApi,
  getOrderApi
} from "@/api/order";

const { jobAgentList } = storeToRefs(useUserStore());

const { back } = useBackRoute();

const tipStr = ref<string>("");
const form = ref<AppealReq>({
  voucher: [],
  orderid: undefined,
  isB2C: false,
  note: ""
});

const onOversize = () => {
  showToast("文件大小不能超过3M");
};

const validaOrder = (val: string): Promise<boolean | string> => {
  return new Promise((resolve, reject) => {
    showLoadingToast({});
    getOrderApi({ orderid: val })
      .then(res => {
        resolve(true);
        closeToast();
      })
      .catch(err => {
        resolve("订单号不正确");
        closeToast();
      });
  });
};

const getExist = (orderId: string) => {
  if (!orderId) return;
  getAppealExistApi({
    orderId: orderId
  }).then(res => {
    tipStr.value = res.exist
      ? "该笔订单已经有其他销售正在申诉，请确认是否仍要申诉？"
      : "";
  });
};

const validaPic = () => {
  if (form.value.voucher.length < 3) {
    return "至少要选择3张图片";
  }
  return true;
};

const onSubmit = () => {
  showLoadingToast({});
  const params = cloneDeep(form.value);
  const arr: string[] = [];
  params.voucher?.forEach(item => {
    if (item.link) {
      arr.push(item.link);
    }
  });
  params.voucher = arr;
  addAppealApi(params).then(() => {
    showToast("操作成功");
    setTimeout(() => {
      back(-1, ["customer"]);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form
      class="mt-20px"
      label-align="right"
      label-width="60px"
      @submit="onSubmit"
    >
      <van-cell-group inset>
        <van-field
          required
          v-model="form.orderid"
          label="订单号"
          placeholder="请输入订单号"
          :rules="[
            { required: true, message: '请输入订单号', trigger: 'onBlur' },
            { validator: validaOrder, trigger: 'onBlur' }
          ]"
          @blur="getExist(form.orderid as string)"
        />
        <div v-if="tipStr" class="text-center text-24px c-orange w-100%">
          {{ tipStr }}
        </div>
        <van-field
          v-model="form.note"
          rows="3"
          autosize
          label="备注"
          type="textarea"
          placeholder="请输入备注"
        />
        <van-field
          required
          readonly
          is-link
          label="图片"
          :rules="[
            { required: true, message: '请选择图片', trigger: 'onChange' },
            { validator: validaPic, trigger: 'onChange' }
          ]"
        >
          <template #input>
            <MyUpload
              v-model:value="form.voucher"
              :maxSize="3 * 1024 * 1024"
              :maxCount="12"
              multiple
              @oversize="onOversize"
            />
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped></style>
