<!--
 * @Date         : 2025-02-19 11:40:52
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
  * @Date         : 2025-02-18 17:01:40
  * @Description  :
  * @Autor        : xiaozhen
  * @LastEditors  : xiaozhen
-->

<template>
  <iframe
    ref="IframeRef"
    width="100%"
    :src="url"
    height="667"
    frameborder="0"
  />
</template>

<script lang="ts" setup>
import { showToast } from "vant";
import { watch, ref, onMounted, onUnmounted } from "vue";

const props = defineProps<{
  userId: string;
}>();
const IframeRef = ref();

const url = ref(
  `${import.meta.env.VITE_H5_URL}/subjectAnalysis.html?userId=${props.userId}`
);

watch(
  () => props.userId,
  value => {
    if (value) {
      url.value = `${import.meta.env.VITE_H5_URL}/subjectAnalysis.html?userId=${
        props.userId
      }`;
    }
  }
);

async function listenPostMessage(event) {
  if (event.origin.indexOf("yangcongxing") !== -1) {
    const { type, blob } = event.data;

    if (type === "copyText" || type === "copyImage") {
      const item = new window.ClipboardItem(blob);
      try {
        await navigator.clipboard.write([item]);
        showToast("复制成功");
      } catch (error) {
        console.error(error);
        showToast("暂不支持复制，请前往PC端CRM复制");
      }
    }
  }
}
onMounted(() => {
  window.addEventListener("message", listenPostMessage);
});
onUnmounted(() => {
  window.removeEventListener("message", listenPostMessage);
});
</script>

<style lang="scss" scoped>
.iframe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  padding: 20px 0;
  cursor: pointer;
  background: #ffffff;

  .top-route {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 20px 10px;
    color: #000;
  }

  iframe {
    width: 375px;
    height: 667px;
    padding: 0 20px 20px;
    resize: vertical;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }

  .iframe__copy-url {
    margin-left: auto;
  }
}

.iframe-container-landscape {
  width: 700px;

  iframe {
    width: 667px;
    height: 375px;
    resize: none;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }
}
</style>
