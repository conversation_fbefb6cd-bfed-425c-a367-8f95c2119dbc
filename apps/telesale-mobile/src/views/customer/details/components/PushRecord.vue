<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-31 14:27:50
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-04-27 14:24:58
 * @FilePath: /telesale-web_v2/apps/telesale-mobile/src/views/customer/details/components/PushRecord.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { pushPayRecordApi, PushRecordType } from "@/api/customer/details";
import { timeChange } from "@/utils/common";
import { showLoadingToast, closeToast } from "vant";
import { useRoute } from "vue-router";
import {
  getCouresName,
  getPushType,
  setAddContentName
} from "@telesale/shared";
import { getSyncDataApi } from "@/api/customer/exclusiveLink";

const list = ref<PushRecordType[]>([]);
const route = useRoute();
const { allAgentObj } = storeToRefs(useUserStore());

const getList = () => {
  const { userId } = route.query;
  if (!userId) return;
  showLoadingToast({});
  pushPayRecordApi({ userId })
    .then(async res => {
      list.value = res.list;
      await setAddContentName(list.value, getSyncDataApi);
    })
    .catch(err => {
      list.value = [];
    })
    .finally(() => {
      closeToast();
    });
};

getList();
</script>

<template>
  <div class="container-order">
    <div v-if="list.length > 0" class="order-list">
      <div v-for="(item, index) in list" :key="index" class="order-item mb-4">
        <van-cell-group :border="false">
          <van-cell
            :border="false"
            title="推送类型"
            :value="getPushType(item)"
          />
          <van-cell
            :border="false"
            title="课程名称"
            :value="getCouresName(item)"
          />
          <van-cell
            :border="false"
            title="推送时间"
            :value="timeChange(item.createdAt, 3)"
          />
          <van-cell :border="false" title="支付状态" :value="item.payStatus" />
          <van-cell
            :border="false"
            title="推送人"
            :value="allAgentObj?.[item.workerId]?.name"
          />
        </van-cell-group>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container-order {
  padding: 20px;
  box-sizing: border-box;
  :deep(.van-cell__title) {
    flex: none;
    width: 200px;
  }
  :deep(.van-cell__value) {
    width: calc(100% - 200px);
  }
}
</style>
