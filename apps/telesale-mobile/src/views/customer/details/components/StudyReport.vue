<template>
  <div class="flex justify-center gap-20px my-10px">
    <van-button
      type="primary"
      size="small"
      @click="clickCapture"
      :loading="!loadingPage"
    >
      一键截图当前页面
    </van-button>
    <van-button type="primary" size="small" @click="clickQrCode">
      生成小程序二维码
    </van-button>
  </div>
  <div class="relative">
    <div class="absolute top-10px w-100% flex justify-between">
      <van-button
        round
        size="small"
        @click="back"
        :disabled="routeList.length === 0"
      >
        <van-icon name="arrow-left" color="#000" />
      </van-button>
      <van-button type="primary" size="small" @click="copyUrl">
        复制链接
      </van-button>
      <van-button round size="small" @click="refresh">
        <van-icon name="replay" color="#000" />
      </van-button>
    </div>
    <iframe
      ref="IframeRef"
      :src="url"
      :width="iframeWidth"
      :height="iframeHeight"
      @load="loadIframe"
    />
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { getIframeTokenApi } from "@/api/customer/details";
import { closeToast, showLoadingToast, showToast } from "vant";
import { forwardApi } from "@/api/common/index";
import { convertImageUrlToBase64 } from "@/utils/common/imgUrlTransform";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";

const props = defineProps<{
  userId: string;
}>();
type Orientation = "portrait" | "horizontal";
const routeList = ref<{ orientation: Orientation }[]>([]);
const forwardList = ref<{ orientation: Orientation }[]>([]);
const currentStatus = ref<Orientation>("portrait");
const url = ref(
  `${import.meta.env.VITE_7to12_HOST}/onion-learning/study-report/404`
);
const studyReportTab = ref("");
const loading = ref(false);
const loadingPage = ref(false);
const isOldReport = ref(false);
const studyReportDateDaily = ref("");
const studyReportDateWeekly = ref("");
const studyReportTextbook = ref({ stageId: Number, subjectId: Number });
const iframeHeight = ref(window.innerHeight - 140);
const iframeWidth = ref(window.innerWidth + "px");
const qrCardModal = ref(false);
const qrLink = ref<string>("");
const IframeRef = ref();
const show = ref(false);
const isMessage = ref(false);
const imgUrl = ref("");
const token = ref("");

function init() {
  const omvd = Math.random().toString(36).substring(2, 12);
  getIframeTokenApi({
    userId: props.userId,
    source: "dianxiao"
  }).then(res => {
    token.value = res.token;
    url.value = `${
      import.meta.env.VITE_7to12_HOST
    }/onion-learning/study-report/?token=${
      res.token
    }&channel=crm&fromPageName=crm&userId=${props.userId}&omvd=${omvd}`;
  });
}

props.userId && init();

const copyUrl = () => {
  const omvd = Math.random().toString(36).substring(2, 12);

  const input = document.createElement("input");
  input.setAttribute(
    "value",
    `${
      import.meta.env.VITE_7to12_HOST
    }/onion-learning/study-report/?token=${encodeURIComponent(
      token.value
    )}&channel=crm_msg&fromPageName=crm_msg&userId=${
      props.userId
    }&origin=wuhan_msg&omvd=${omvd}`
  );
  document.body.appendChild(input);
  input.select();
  document.execCommand("copy");
  showToast("复制成功");
  document.body.removeChild(input);
  showToast("复制成功");
};

const loadIframe = () => {
  if (url.value) {
    loadingPage.value = true;
  }
};

const back = () => {
  if (!routeList.value.length) return;
  const route = routeList.value.pop();
  const last = routeList.value[routeList.value.length - 1];
  currentStatus.value = last?.orientation || "portrait";
  forwardList.value.push(route!);
  window.history.go(-1);
};

const refresh = () => {
  const link = url.value;
  url.value = "";
  currentStatus.value = "portrait";
  routeList.value = [];
  setTimeout(() => {
    url.value = link;
  }, 0);
};

/** CRM分享截图 */
const clickCapture = () => {
  if (isOldReport.value) {
    showToast("旧学情报告不支持截图～");
    return;
  }
  showLoadingToast({});
  isMessage.value = false;
  setTimeout(() => {
    if (!isMessage.value) {
      showToast("该页面暂不支持截图");
    }
  }, 3000);
  // screenShot(IframeRef.value)
  IframeRef.value.contentWindow.postMessage(
    {
      type: "SCREENSHOT_START"
    },
    "*"
  );
};

const envVersion = () => {
  switch (import.meta.env.VITE_ENV) {
    case "test":
      return "develop";
    case "stage":
      return "trial";
    case "master":
      return "release";
    default:
      return "develop";
  }
};

const generateQrCode = data => {
  return new Promise((resolve, reject) => {
    let scene = "";
    forwardApi({
      targetReal: "study-parent.7to12",
      targetPath: "/study-parent/wechat/saveShareLoginKey",
      method: "post",
      ...data
    })
      .then(res => {
        if (res?.data) {
          scene = res.data?.key || "";
        }
      })
      .finally(() => {
        forwardApi({
          targetReal: "wechat-base.7to12",
          targetPath: "/wechat-base/mini_program/qrcode",
          method: "post",
          appid: "wxad8a35ca577b3b8d",
          scene: `yc${scene}`,
          envVersion: envVersion()
        })
          .then(data => {
            if (data.image) {
              convertImageUrlToBase64(data.image)
                .then(base64 => {
                  resolve(base64);
                })
                .catch(err => {
                  reject(err);
                });
            } else {
              reject(new Error(data.errmsg));
            }
          })
          .catch(err => {
            reject(err);
          });
      });
  });
};

/** CRM分享生成二维码 */
const clickQrCode = () => {
  showLoadingToast({});
  generateQrCode({
    scene: "STUDY_REPORT_SHARE",
    uid: props.userId,
    expireSecond: 3600 * 24,
    data: {
      tab: studyReportTab.value,
      date:
        studyReportTab.value === "daily"
          ? studyReportDateDaily.value
          : studyReportDateWeekly.value,
      fromPageName: "crm_copyReportQRCode"
    }
  })
    .then((res: any) => {
      imgUrl.value = res;
      show.value = true;
    })
    .catch(err => {
      console.log(err);

      showToast("生成小程序二维码失败");
    })
    .finally(() => {
      closeToast();
    });
};

async function screenshotMessage(event) {
  isMessage.value = true;
  if (event.data?.type === "SHOW_REPORT_NEW") {
    isOldReport.value = false;
  } else if (event.data?.type === "SHOW_REPORT_OLD") {
    isOldReport.value = true;
  }
  if (!event.data?.type?.includes("SCREENSHOT")) {
    return;
  }
  const { height, type, error, blob } = event.data;

  if (height) {
    iframeHeight.value = height;
    await nextTick();
  }
  iframeWidth.value = "375px";
  if (type === "SCREENSHOT_END") {
    await nextTick();
    iframeHeight.value = window.innerHeight - 140;
    iframeWidth.value = "100%";
    closeToast();
    if (error) {
      showToast(`截图生成失败:${error}`);
    } else {
      showToast("截图生成成功");
      const fileReader = new FileReader();
      fileReader.onload = function (e) {
        const base64String = window.btoa(e.target?.result as string);
        imgUrl.value = `data:image/jpeg;base64,${base64String}`;
        show.value = true;
      };
      fileReader.readAsBinaryString(blob);
      // copyToClipboard(blob)
      //   .then(() => {
      //     showToast("学情报告图片复制成功");
      //   })
      //   .catch(err => {
      //     console.error("学情报告图片复制失败", err);
      //     showToast(`学情报告图片复制失败: ${err.message || err.msg}`);
      //   });
    }
  }
}
onMounted(() => {
  window.addEventListener("message", listenPostMessage);
  window.addEventListener("message", screenshotMessage);
});
onUnmounted(() => {
  window.removeEventListener("message", listenPostMessage);
  window.removeEventListener("message", screenshotMessage);
});
onDeactivated(() => {
  window.removeEventListener("message", listenPostMessage);
  window.removeEventListener("message", screenshotMessage);
});

const listenPostMessage = event => {
  // 验证消息来源，确保安全
  if (
    event.origin.indexOf("yangcongxing") !== -1 ||
    event.origin.indexOf("5173") !== -1
  ) {
    const { type, orientation, tab, date, url: link, textbook } = event.data;
    if (type === "ROUTE_CHANGE") {
      currentStatus.value = orientation;
      routeList.value.push({ orientation });
    } else if (type === "TAB_CHANGE") {
      // CRM分享截图参数
      studyReportTab.value = tab;
    } else if (type === "DATE_DAILY_CHANGE") {
      // CRM分享截图参数
      studyReportDateDaily.value = date;
    } else if (type === "DATE_WEEKLY_CHANGE") {
      // CRM分享截图参数
      studyReportDateWeekly.value = date;
    } else if (type === "URL_CHANGE") {
      // url.value = link;
      console.log("1111111111 URL_CHANGE", link);
    } else if (type === "TEXTBOOK_CHANGE") {
      // CRM分享截图参数
      studyReportTextbook.value = textbook;
      console.log("1111111111 TEXTBOOK_CHANGE", textbook);
    }
  }
};
</script>

<style lang="scss" scoped></style>
