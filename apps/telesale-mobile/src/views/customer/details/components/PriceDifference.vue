<script lang="ts" setup>
import { useRouter } from "vue-router";
import {
  priceDiffFind,
  repurchaseFind,
  getTrialOrderListApi
} from "@/api/customer/details";
import { usePriceDifference } from "@telesale/shared";
import { bigVipList } from "@telesale/shared/src/data/customer";
import { getAuth } from "@/utils/common/auth";
import { closeToast, showLoadingToast, showToast } from "vant";
import { findPositionApi } from "@/api/user";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { isExperimentGroupWorkerApi } from "@/api/common";

interface Props {
  userid?: string;
  isShowOperation: boolean;
  type?: string;
  bigVip?: boolean;
}

const props = defineProps<Props>();

const { userMsg } = storeToRefs(useUserStore());

const router = useRouter();

const mode = import.meta.env.MODE;

const {
  loading,
  description,
  dataList,
  repurchaseType,
  activePadList,
  listHeader,
  searchForm,
  trialList,
  filterData
} = usePriceDifference({
  query: props,
  mode,
  isRepurchase: getAuth("telesale_admin_repurchase"),
  repurchaseFind,
  priceDiffFind,
  getTrialOrderListApi,
  getOrgData: () =>
    findPositionApi({ key: "id", value: userMsg.value.id + "" }),
  isExperimentGroupWorkerApi: () =>
    isExperimentGroupWorkerApi({ workerId: userMsg.value.id as number })
});

const goAddCode = (path: string, child) => {
  router.push({
    path,
    query: {
      uri: child.payPage,
      strategyType: child.strategyType,
      dynamic: "true",
      cloneName: child.cloneName
    }
  });
};

const goCode = (path: string, child) => {
  router.push({
    path,
    query: {
      type: "diffFind",
      repurchaseType: repurchaseType.value,
      orderId: child.orderId,
      name: child.name,
      goodName: child.cloneName,
      uri: child.payPage,
      cloneName: child.cloneName
    }
  });
};

const searchData = () => {
  if (searchForm.value.amount) {
    const amountValue = Number(searchForm.value.amount);
    if (!isFinite(amountValue)) {
      showToast({
        message: "金额需要为数字",
        duration: 3000
      });
      return;
    }
    if (amountValue <= 0 || amountValue > 99999999) {
      showToast({
        message: "金额需要大于0，小于99999999",
        duration: 3000
      });
      return;
    }
  }
  filterData();
};

const getEndTime = list => {
  let time = "";
  list?.forEach(item => {
    if (item.endTime) {
      if (time) {
        time =
          new Date(time).getTime() > new Date(item.endTime).getTime()
            ? time
            : item.endTime;
      } else {
        time = item.endTime;
      }
    }
  });
  return time;
};

watch(
  () => loading.value,
  n => {
    n ? showLoadingToast({}) : closeToast();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="diff">
    <van-sticky :offset-top="44">
      <div class="bg-white py-2">
        <div class="px-2 gap-2 flex items-center">
          <div class="search-item w-50% flex justify-between">
            <!-- {{ searchForm.goodName || "请输入商品名称" }} -->
            <van-search
              v-model="searchForm.goodName"
              placeholder="请输入商品名称"
              class="w-100% p-0 bg-#eef2fb!"
              style="height: 22px"
              @search="filterData"
              :clearable="false"
            />

            <van-icon name="search" />
          </div>
          <div class="search-item w-50% flex justify-between">
            <van-search
              v-model="searchForm.amount"
              placeholder="请输入商品金额"
              class="w-100% p-0 bg-#eef2fb!"
              style="height: 22px"
              @search="searchData"
              :clearable="false"
            />

            <van-icon name="search" />
          </div>
        </div>
        <div class="px-20px mt-20px">
          <van-radio-group
            v-model="searchForm.bigVipType"
            direction="horizontal"
            @change="filterData"
          >
            <van-radio
              icon-size="12"
              v-for="item in bigVipList"
              :key="item.value"
              :name="item.value"
            >
              {{ item.label }}
            </van-radio>
          </van-radio-group>
        </div>
      </div>
    </van-sticky>
    <div v-if="trialList?.length > 0">
      <div
        class="c-red gap-20px mb-5px px-40px text-28px my-10px"
        v-for="(item, index) in trialList"
        :key="index"
      >
        <div>体验机订单：{{ item.orderId }}</div>
        <div>体验周期：{{ item.trialStartTime }}至 {{ item.trialEndTime }}</div>
        <div>是否锁机：{{ item.deviceLock ? "是" : "否" }}</div>
      </div>
    </div>
    <div
      class="text-28px mb-10px c-red ml-40px mt-20px"
      v-if="getEndTime(dataList) && props.bigVip"
    >
      续购资格失效时间：{{ getEndTime(dataList) }}
    </div>
    <template v-if="activePadList.length > 0">
      <template v-for="(item, index) in activePadList" :key="index">
        <div class="text-32px font-bold my-20px">
          {{ item.padType ? `带${item.padType}` : "不带" }}平板商品：
        </div>
        <div class="group">
          <div class="group-item" v-for="child in item.list" :key="child.id">
            <div class="good-name" v-html="child.goodName" />
            <div class="flex flex-wrap">
              <template v-for="item in listHeader" :key="item.field">
                <template v-if="item.field !== 'goodName'">
                  <div class="good-info" v-if="!item.htmlChange">
                    {{ item.desc }}
                    {{
                      `${
                        item.filters ? item.filters(child) : child[item.field]
                      }`
                    }}
                  </div>
                  <div class="good-info" v-else>
                    {{ item.desc }}
                    <span v-html="child[item.field]" />
                  </div>
                </template>
              </template>
            </div>
            <div>
              <div>
                <template v-if="child['strategyTypeName'] === '新禧商品'">
                  <div
                    class="qrcode-btn"
                    @click="goAddCode('/customer/addQrcode', child)"
                  >
                    创建动态二维码
                  </div>
                </template>
                <template v-else>
                  <div
                    class="qrcode-btn"
                    @click="goCode('/customer/qrCode', child)"
                  >
                    动态二维码
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </template>
    </template>

    <van-empty v-else :description="description" />
  </div>
</template>

<style lang="scss" scoped>
.diff {
  :deep(.van-search) {
    height: 44px;
    padding: 0;
  }
  :deep(.van-search__content) {
    padding-left: 0;
  }
  :deep(.van-field__control) {
    background-color: #eef2fb;
    color: #3370ff !important;
    &::placeholder {
      color: #3370ff !important;
      opacity: 1;
    }
  }
  :deep(.van-search__field .van-field__left-icon) {
    display: none;
  }
  .search-item {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    background-color: #eef2fb;
    color: #3370ff;
    border-radius: 10px;
    height: 32px;
  }
  .diff-list {
    padding: 0 20px;
    .show-title {
      padding: 10px 20px;
      background-color: #fae9fa;
      color: #6c0abc;
      border-radius: 10px;
      margin: 20px 0 20px 10px;
    }
    .group {
      padding: 0 20px;
      border-radius: 20px;
      width: 100%;
      background-color: #fff;
      box-sizing: border-box;
      &-item {
        padding: 20px;
        border-bottom: 1px solid #b4b4b4;
      }
      &-item:last-child {
        border-bottom: 0;
      }
      .good-name {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 20px;
      }
      .good-info {
        padding: 10px 20px;
        background-color: #fff9e9;
        color: #813300;
        font-size: 24px;
        border-radius: 10px;
        margin-right: 20px;
        margin-bottom: 10px;
      }
      .qrcode-btn {
        color: #3370ff;
        font-size: 24px;
        margin-top: 20px;
      }
    }
  }
}
.show-title {
  padding: 10px 20px;
  background-color: #fae9fa;
  color: #6c0abc;
  border-radius: 10px;
  margin: 20px 0 20px 10px;
}
.group {
  padding: 0 20px;
  border-radius: 20px;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  &-item {
    padding: 20px;
    border-bottom: 1px solid #b4b4b4;
  }
  &-item:last-child {
    border-bottom: 0;
  }
  .good-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .good-info {
    padding: 10px 20px;
    background-color: #fff9e9;
    color: #813300;
    font-size: 24px;
    border-radius: 10px;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
</style>
