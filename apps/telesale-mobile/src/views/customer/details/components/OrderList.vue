<script lang="ts" setup>
import { getOrderListApi } from "@/api/customer/details";
import { OrederType } from "@/types/customer/details";
import { timeChange } from "@/utils/common";
import { showLoadingToast, closeToast } from "vant";

const props = defineProps<{
  userId?: string;
}>();

const list = ref<OrederType[]>([]);

const getList = () => {
  if (!props.userId) return;
  showLoadingToast({});
  getOrderListApi({ userid: props.userId })
    .then(res => {
      list.value = res;
    })
    .catch(() => {
      list.value = [];
    })
    .finally(() => {
      closeToast();
    });
};

getList();
</script>

<template>
  <div class="container-order">
    <div v-if="list.length > 0" class="order-list">
      <div v-for="(item, index) in list" :key="index" class="order-item mb-4">
        <van-cell-group :border="false">
          <van-cell :border="false" title="订单号" :value="item._id" />
          <van-cell :border="false" title="商品名称" :value="item.good.name" />
          <van-cell
            :border="false"
            title="创建时间"
            :value="timeChange(item.createdAt, 3)"
          />
          <van-cell
            :border="false"
            title="支付成功时间"
            :value="timeChange(item.paidTime, 3)"
          />
          <van-cell :border="false" title="订单状态" :value="item.status" />
          <van-cell
            :border="false"
            title="订单总价"
            :value="item.good.amount"
          />
          <van-cell :border="false" title="实付价格" :value="item.amount" />
          <van-cell
            :border="false"
            title="支付平台"
            :value="item.paymentPlatform"
          />
        </van-cell-group>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container-order {
  padding: 20px;
  box-sizing: border-box;
  :deep(.van-cell__title) {
    flex: none;
    width: 200px;
  }
  :deep(.van-cell__value) {
    width: calc(100% - 200px);
  }
}
</style>
