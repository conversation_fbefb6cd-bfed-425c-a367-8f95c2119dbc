<!--
 * @Date         : 2024-11-26 10:10:59
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import PriceDifference from "./PriceDifference.vue";
import Renew from "./Renew.vue";

const props = defineProps<{
  userid: string;
  isShowOperation: boolean;
}>();

const active = ref(0);
</script>

<template>
  <div>
    <van-tabs v-model:active="active">
      <van-tab title="活动续购">
        <PriceDifference
          :userid="props.userid"
          :key="props.userid ? 1 : 2"
          :isShowOperation="true"
          type="diffFind"
          bigVip
        />
      </van-tab>
      <van-tab title="普通续购">
        <Renew
          :userid="props.userid"
          :isShowOperation="props.isShowOperation"
          type="repurchase"
        />
      </van-tab>
    </van-tabs>
  </div>
</template>

<style lang="scss" scoped></style>
