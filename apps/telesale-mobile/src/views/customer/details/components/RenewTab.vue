<!--
 * @Date         : 2024-11-26 10:10:59
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup>
import Renew from "./Renew.vue";

const props = defineProps<{
  userid: string;
  isShowOperation: boolean;
}>();
</script>

<template>
  <div>
    <Renew
      :userid="props.userid"
      :isShowOperation="props.isShowOperation"
      type="repurchase"
    />
  </div>
</template>

<style lang="scss" scoped></style>
