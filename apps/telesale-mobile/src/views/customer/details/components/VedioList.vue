<script lang="ts" setup>
import { videoHistoryApi } from "@/api/customer/details";
import { VideoRecordType } from "@/types/customer/details";
import { timeChange } from "@/utils/common";
import { showLoadingToast, closeToast } from "vant";

const props = defineProps<{
  userId?: string;
}>();

const list = ref<VideoRecordType[]>([]);

const getList = () => {
  if (!props.userId) return;
  showLoadingToast({});
  videoHistoryApi(props.userId)
    .then(res => {
      list.value = res;
    })
    .catch(() => {
      list.value = [];
    })
    .finally(() => {
      closeToast();
    });
};

getList();
</script>

<template>
  <div class="container-order">
    <div v-if="list.length > 0" class="order-list">
      <div v-for="(item, index) in list" :key="index" class="order-item mb-4">
        <van-cell-group :border="false">
          <van-cell :border="false" title="学期" :value="item.semesterName" />
          <van-cell :border="false" title="学科" :value="item.subjectName" />
          <van-cell :border="false" title="教材" :value="item.publisherName" />
          <van-cell :border="false" title="视频名称" :value="item.video.name" />
          <van-cell
            :border="false"
            title="知识点名称"
            :value="item.topic.name"
          />
          <van-cell
            :border="false"
            title="是否付费内容"
            :value="
              item.topic.isFreeTime ? '限免' : item.topic.pay ? '付费' : '免费'
            "
          />
          <van-cell
            :border="false"
            title="观看时间"
            :value="timeChange(item.timestamp, 2)"
          />
        </van-cell-group>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container-order {
  padding: 20px;
  box-sizing: border-box;
  :deep(.van-cell__title) {
    flex: none;
    width: 200px;
  }
  :deep(.van-cell__value) {
    width: calc(100% - 200px);
  }
}
</style>
