<!--
 * @Date         : 2024-05-09 18:12:48
 * @Description  : 赠送资料
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import {
  GiveProfile,
  giveProfile<PERSON>pi,
  ProfileInfo,
  getProfile<PERSON>pi
} from "@/api/customer/action";
import { showLoadingToast, showToast } from "vant";
import { useRoute } from "vue-router";
import { useBackRoute } from "@/hooks/router/useBackRoute";
import MySelect from "@/components/MySelect/index.vue";

const route = useRoute();
const { back } = useBackRoute();

const { userId } = route.query || {};

const profileList = ref<ProfileInfo[]>([]);
const form = ref<GiveProfile>({
  userId: userId as string,
  ids: []
});

const getProfileList = () => {
  getProfileApi().then(res => {
    profileList.value = res.list;
  });
};

getProfileList();

const onSubmit = () => {
  showLoadingToast({});
  giveProfileApi(form.value).then(res => {
    if (res.failNum > 0) {
      showToast(`发送成功${res.successNum}个，失败${res.failNum}个`);
    } else {
      showToast(`发送成功${res.successNum}个`);
    }
    setTimeout(() => {
      back(-1, []);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <div class="d-tip-box">
      <van-icon name="warning-o" />
      <span>
        专属资料将被发送至APP-我的-消息中心-通知中，查看资料需要用户的 APP版本在
        7.59.0 及以上。
      </span>
    </div>
    <van-form
      required
      class="mt-20px"
      label-width="80px"
      label-align="right"
      @submit="onSubmit"
    >
      <van-cell-group inset>
        <MyMultiple
          v-model:value="form.ids"
          label="赠送资料"
          placeholder="请选择资料"
          :columns="profileList"
          :options="{
            label: 'materialName',
            value: 'id'
          }"
          :rules="[
            { required: true, message: '请选择资料', trigger: 'onChange' }
          ]"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped>
.d-tip-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #eaf5ff;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 24px;
}
</style>
