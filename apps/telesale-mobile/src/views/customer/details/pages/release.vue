<script lang="ts" setup>
import { ref } from "vue";
import { ExpireForm } from "@/types/customer/action";
import { releaseApi } from "@/api/customer/action";
import { showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import { useBackRoute } from "@/hooks/router/useBackRoute";
import MySelect from "@/components/MySelect/index.vue";

const route = useRoute();
const { back } = useBackRoute();

const isTime = ref<boolean>(false);
const form = ref<ExpireForm>({
  infoUuid: route.query.infoUuid as string,
  expire: undefined,
  isEndService: false
});

const expireList: LabelValueOption[] = [
  { label: "不设置冷静期", value: 0 },
  { label: "30天", value: 1 },
  { label: "60天", value: 2 },
  { label: "90天", value: 3 },
  { label: "1年", value: 5 },
  { label: "2年", value: 6 },
  { label: "3年", value: 4 }
];

const onSubmit = () => {
  showLoadingToast({});
  releaseApi(form.value).then(() => {
    showToast("操作成功");
    setTimeout(() => {
      back(-2, ["customer"]);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <MySelect
          v-model:value="form.expire"
          name="picker"
          label="冷静期"
          placeholder="请选择冷静期"
          :columns="expireList"
          required
          :rules="[
            { required: true, message: '请选择冷静期', trigger: 'onChange' }
          ]"
        />
        <van-field name="radio" label="是否同时终结服务期" required>
          <template #input>
            <van-radio-group v-model="form.isEndService" direction="horizontal">
              <van-radio :name="false">否</van-radio>
              <van-radio :name="true">是</van-radio>
            </van-radio-group>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped></style>
