<script lang="ts" setup>
import { ref } from "vue";
import { cloneDeep } from "lodash-es";
import { PursuitForm } from "@/types/customer/action";
import { addCallRecordApi, intentionApi } from "@/api/customer/action";
import { showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import MyDatePicker from "@/components/MyDatePicker/index.vue";
import dayjs from "dayjs";

const route = useRoute();
const router = useRouter();

const isTime = ref<boolean>(false);
const form = ref<PursuitForm>({
  infoUuid: route.query.infoUuid as string,
  intention: "",
  note: "",
  notifyTime: ""
});

const intentionList = ref<LabelValueOption[]>([]);
const defaultTime = computed(() => {
  const time = localStorage.getItem("documentaryRecordTime");
  const currentTime = dayjs().format("YYYY-MM-DD");
  return time
    ? dayjs(`${currentTime} ${time}`).format("YYYY-MM-DD HH:mm:ss")
    : dayjs(`${currentTime} 18:00:00`).format("YYYY-MM-DD HH:mm:ss");
});

const getIntentionList = () => {
  intentionApi().then(res => {
    if (res) {
      res.forEach(item => {
        const label = Object.keys(item)[0];
        const white = ["未拨", "未沟通成单", "正常成单"];
        if (!white.includes(label)) {
          intentionList.value.push({
            label,
            value: Object.values(item)[0]
          });
        }
      });
    }
  });
};

getIntentionList();

const onSubmit = () => {
  const params = cloneDeep(form.value);
  params.notifyTime = isTime.value
    ? params.notifyTime
      ? Math.floor(+new Date(params.notifyTime) / 1000)
      : 0
    : 0;
  showLoadingToast({});
  addCallRecordApi(params).then(() => {
    showToast("操作成功");
    console.log(params, "params");
    if (params.notifyTime && isTime.value) {
      localStorage.setItem(
        "documentaryRecordTime",
        dayjs(form.value.notifyTime).format("HH:mm:ss")
      );
    }
    setTimeout(() => {
      router.go(-1);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <MySelect
          v-model:value="form.intention"
          name="picker"
          label="意向度"
          placeholder="请选择意向度"
          :columns="intentionList"
          required
          :rules="[
            { required: true, message: '请选择意向度', trigger: 'onChange' }
          ]"
        />
        <van-field name="switch" label="是否选择下次跟单时间">
          <template #input>
            <van-switch v-model="isTime" size="20px" />
          </template>
        </van-field>
        <MyDatePicker
          v-if="isTime"
          v-model:value="form.notifyTime"
          :default-time="defaultTime"
          name="datePicker"
          label="时间选择"
          placeholder="点击选择时间"
        />
        <van-field
          v-model="form.note"
          rows="3"
          autosize
          label="追单记录"
          type="textarea"
          placeholder="请输入追单记录"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped></style>
