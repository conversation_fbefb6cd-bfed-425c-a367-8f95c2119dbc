<script lang="ts" setup>
import { ref } from "vue";
import { differenceQrcodeApi, DiifPriceParams } from "@/api/customer/details";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import { schoolYearList, useQrcode } from "@telesale/shared";
import { getAuth } from "@/utils/common/auth";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";
import { getArrayBufferBase64 } from "@/utils/common";

const { query } = useRoute();

const router = useRouter();
// const { qrcodeList, qrcodeType, qrcodeValue } = useQrcode(getAuth);

const form = ref<DiifPriceParams>({
  schoolYear: "",
  from: "telesale",
  dynamic: query.dynamic === "true" ? true : false,
  strategyType: query.strategyType as string,
  uri: query.uri as string,
  name: query.cloneName as string,
  courseName: query.cloneName as string
});

const imgUrl = ref<string>("");
const show = ref<boolean>(false);

const onSubmit = () => {
  showLoadingToast({});
  // qrcodeType.value = form.value.from;
  differenceQrcodeApi(form.value).then(async res => {
    const url = await getArrayBufferBase64(res);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <!-- <MySelect
          v-model:value="form.from"
          label="项目组"
          placeholder="请选择项目组"
          :columns="qrcodeList"
          :rules="[
            { required: true, message: '请选择项目组', trigger: 'onChange' }
          ]"
        /> -->
        <MySelect
          v-model:value="form.schoolYear"
          label="年级"
          placeholder="请选择年级"
          :columns="schoolYearList"
          :options="{
            label: 'text',
            value: 'value'
          }"
          :rules="[
            { required: true, message: '请选择年级', trigger: 'onChange' }
          ]"
        />
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
