<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { getDoneListApi } from "@/api/customer/done";
import { timeChange } from "@/utils/common/index";
import { sourceList } from "@/utils/data/index";
import { useSearch } from "@/hooks/useSearch";
import { useRouter } from "vue-router";
import MyList from "@/components/MyList/index.vue";

const props = withDefaults(
  defineProps<{
    immediate?: boolean;
  }>(),
  {
    immediate: true
  }
);

const { allAgentObj, userMsg } = storeToRefs(useUserStore());
const router = useRouter();

const { list, loading, finished, error, onLoad, onSearch, searchForm } =
  useSearch({
    api: getDoneListApi,
    immediate: props.immediate,
    initParams: {
      callCount: -1
    }
  });

const goDetail = (infoUuid: string, phone: string, onionid: string) => {
  router.push({
    path: "/customer/details",
    query: {
      infoUuid,
      phone,
      onionid
    }
  });
};

defineExpose({
  onSearch,
  searchForm
});
</script>

<template>
  <div class="h-100%">
    <MyList
      v-model:error="error"
      :list="list"
      :loading="loading"
      :finished="finished"
      :onLoad="onLoad"
    >
      <template #default="{ data }">
        <div
          class="customer py-4"
          @click="goDetail(data.infoUuid, data.phone, data.onionid)"
        >
          <div class="flex mb-20px">
            <div class="mr-40px">手机号 {{ data.phone }}</div>
            <div>洋葱ID {{ data.onionid }}</div>
          </div>
          <div class="mb-20px">订单ID {{ data.orderid }}</div>
          <div class="mb-20px">课程名称 {{ data.good.name }}</div>
          <div class="flex flex-wrap gap-20px">
            <div class="tag" v-if="userMsg.leafNode">
              {{ allAgentObj?.[data.workerid]?.name }}
            </div>
            <div class="tag">成交额 {{ data.amount }}</div>
            <div class="tag">到期时间 {{ timeChange(data.userExpire, 2) }}</div>
          </div>
        </div>
      </template>
    </MyList>
  </div>
</template>

<style lang="scss" scoped>
.customer {
  border-bottom: 1px solid #b4b4b4;
  .tag {
    padding: 4px 10px;
    background-color: #fff9e9;
    color: #a2653a;
    font-size: 24px;
  }
}
</style>
