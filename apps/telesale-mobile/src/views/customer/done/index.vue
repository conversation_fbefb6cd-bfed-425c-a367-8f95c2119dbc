<script lang="ts" setup name="customer">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { useRouter } from "vue-router";
import CustomerList from "./components/CustomerList.vue";
import SearchSelect from "@/components/SearchSelect/index.vue";

const { agentList, userMsg } = storeToRefs(useUserStore());

const customerListRef = ref<InstanceType<typeof CustomerList>>();
const agent = ref<string>();
const agentName = ref<string>("");
const isShow = ref<boolean>(false);
const router = useRouter();

const selectAgent = () => {
  isShow.value = true;
};

const search = () => {
  customerListRef.value!.searchForm.workerid = agent.value
    ? String(agent.value)
    : undefined;
  customerListRef.value?.onSearch();
};

const goSearch = (type: string) => {
  router.push({
    path: "/customer/done/search",
    query: {
      type
    }
  });
};
</script>

<template>
  <div class="container">
    <div class="flex justify-around bg-white py-2">
      <div v-if="userMsg?.leafNode" class="search-item" @click="selectAgent">
        <span class="inline-block truncate max-w-200px">
          {{ agentName || "选择坐席" }}
        </span>
        <van-icon name="arrow-down" />
      </div>
      <div class="search-item" @click="goSearch('phone')">
        搜索手机号
        <van-icon name="search" />
      </div>
      <div class="search-item" @click="goSearch('onionid')">
        搜索洋葱ID
        <van-icon name="search" />
      </div>
    </div>
    <div class="list-content">
      <CustomerList ref="customerListRef" />
    </div>
    <SearchSelect
      v-if="isShow"
      v-model:value="agent"
      v-model:show="isShow"
      v-model:name="agentName"
      :data="agentList"
      title="选择坐席"
      :options="{
        label: 'name',
        value: 'id'
      }"
      @submit="search"
    />
  </div>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  padding-bottom: 20px;
  box-sizing: border-box;
  .search-item {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    background-color: #eef2fb;
    color: #3370ff;
    border-radius: 10px;
  }
  .list-content {
    flex: 1;
    padding: 24px;
    padding-top: 0;
    margin: 20px;
    background-color: #fff;
    border-radius: 20px;
    overflow: auto;
  }
}
</style>
