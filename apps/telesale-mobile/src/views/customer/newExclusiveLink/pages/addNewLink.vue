<!--
 * @Date         : 2025-02-24 16:36:03
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup>
import { ref } from "vue";
import {
  getGoodInfoApi,
  getSyncDataApi,
  createNewQrcode<PERSON><PERSON>
} from "@/api/customer/exclusiveLink";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import { getLabel, getStageGood, useNewSyncLink } from "@telesale/shared";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";

import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { getArrayBufferBase64 } from "@/utils/common";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { getVenueLinkGoodsListApi } from "@/api/customer/linkSetting";

const router = useRouter();
const { isStages } = storeToRefs(useUserStore());

const {
  form,
  syncData,
  goodInfo,
  goodList,
  loading,
  changeUpgrade,
  changeGood,
  changeSchoolYear,
  handelData
} = useNewSyncLink({
  getGoodInfoApi,
  getSyncDataApi,
  getVenueLinkGoodsListApi
});

const imgUrl = ref<string>("");
const show = ref(false);
const goodsMobile = ref();

const myChangeGood = () => {
  form.value.goods = goodList.value.find(item => item.id === goodsMobile.value);
  changeGood();
};

const changeBlocksSchoolYear = () => {
  goodsMobile.value = undefined;
  changeSchoolYear(form.value.schoolYear as unknown as string);
};

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const onSubmit = () => {
  const params = handelData();
  showLoadingToast({});
  createNewQrcodeApi(params).then(async data => {
    const url = await getArrayBufferBase64(data);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};

watch(
  () => loading.value,
  n => {
    n ? showLoadingToast({}) : closeToast();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          label="年级"
          name="schoolYear"
          required
          :rules="[
            {
              required: true,
              message: '请选择年级',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.schoolYear"
              direction="horizontal"
              @change="changeBlocksSchoolYear"
            >
              <van-radio
                icon-size="12"
                v-for="(item, index) in syncData"
                :key="index"
                :name="item.schoolYear"
              >
                {{
                  item.schoolYear === "三年级" ? "一到三年级" : item.schoolYear
                }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <MySelect
          v-model:value="goodsMobile"
          name="picker"
          label="商品"
          placeholder="请选择商品"
          :columns="goodList"
          :options="{
            label: 'strategyName',
            value: 'id'
          }"
          required
          :rules="[
            { required: true, message: '请选择商品', trigger: 'onChange' }
          ]"
          @confim="myChangeGood"
        />
        <van-field
          label="打包购买首购+升单商品"
          name="schoolYear"
          :rules="[
            {
              required: false,
              message: '请选择',
              trigger: 'onChange'
            }
          ]"
          v-if="form.goods?.packGood?.length"
        >
          <template #input>
            <van-radio-group
              v-model="form.isPackGood"
              direction="horizontal"
              @change="changeUpgrade"
            >
              <van-radio icon-size="12" :name="false">否</van-radio>
              <van-radio icon-size="12" :name="true">是</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          label="加购平板"
          name="addPad"
          :rules="[
            {
              required: false,
              message: '请选择',
              trigger: 'onChange'
            }
          ]"
          v-if="form.goods?.addContent?.length"
        >
          <template #input>
            <van-radio-group
              v-model="form.addPad"
              direction="horizontal"
              @change="changeUpgrade"
            >
              <van-radio
                icon-size="12"
                v-for="item in form.goods.addContent"
                :key="item.label"
                :name="item.label"
              >
                {{ item.name }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <template v-if="goodInfo">
          <van-field label="商品原价">
            <template #input>
              ¥ {{ goodInfo.originalAmount?.toFixed(2) }}
            </template>
          </van-field>
          <van-field label="商品售价">
            <template #input>¥ {{ goodInfo.amount?.toFixed(2) }}</template>
          </van-field>
          <van-field label="用户实付价">
            <template #input>
              ¥ {{ (goodInfo.amount - goodInfo.deductAmount)?.toFixed(2) }}
            </template>
          </van-field>
        </template>
        <template v-if="isStages">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                direction="horizontal"
                @change="changeInstallment"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
