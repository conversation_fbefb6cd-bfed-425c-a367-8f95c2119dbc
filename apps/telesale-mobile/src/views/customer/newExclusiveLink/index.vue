<script lang="ts" setup name="customer">
import { ref } from "vue";
import { useRouter } from "vue-router";
import LinkList from "./components/LinkList.vue";
import { typeList } from "@telesale/shared/src/data/exclusiveLink";
import MyPicker from "@/components/MyPicker/index.vue";
import { closeToast, showLoadingToast, showToast } from "vant";
import {
  getTagListApi,
  TagList,
  getDiscountsCardApi
} from "@/api/customer/exclusiveLink";
import { isFinite } from "lodash-es";
import { getArrayBufferBase64 } from "@/utils/common";
import { useVisibleLink } from "@/hooks/useVisibleLink";
import { getAuth } from "@/utils/common/auth";

const { loading, hasPermissions } = useVisibleLink();

const linkListRef = ref<InstanceType<typeof LinkList>>();
const courseType = ref<string>("");
const courseTags = ref<number>();
const courseTagName = ref<string>("");
const name = ref<string>("");
const amount = ref<number | undefined>(undefined);
const isShow = ref<boolean>(false);
const isTagShow = ref<boolean>(false);
const router = useRouter();
const imgUrl = ref<string>("");
const isImgModal = ref<boolean>(false);

const tagList = ref<TagList[]>([]);
const getTagList = (isSearch = false) => {
  showLoadingToast({});
  getTagListApi()
    .then(res => {
      tagList.value = res.data.list;
      if (isSearch) {
        linkListRef.value?.onSearch();
      }
    })
    .catch(() => {
      closeToast();
    });
};
getTagList(true);
const searchTag = ({ label }: { label: string }) => {
  linkListRef.value!.searchForm.courseTags = courseTags.value;
  courseTagName.value = label;
  linkListRef.value?.onSearch();
};

const search = () => {
  linkListRef.value!.searchForm.courseType = courseType.value;
  linkListRef.value?.onSearch();
};

const filterData = () => {
  linkListRef.value!.searchForm.name = name.value;
  linkListRef.value!.searchForm.amount = Number(amount.value) || undefined;
  if (amount.value) {
    const amountValue = Number(amount.value);
    if (!isFinite(amountValue)) {
      showToast({
        message: "金额需要为数字",
        duration: 3000
      });
      return;
    }
    if (amountValue <= 0 || amountValue > 99999999) {
      showToast({
        message: "金额需要大于0，小于99999999",
        duration: 3000
      });
      return;
    }
  }
  linkListRef.value?.onSearch();
};

const clearCourseType = () => {
  courseType.value = "";
  search();
};

const clearCourseTag = () => {
  courseTags.value = undefined;
  searchTag({ label: "" });
};

const addLink = (path: string) => {
  router.push({
    path: "/customer/newExclusiveLink/" + path
  });
};

const openCard = () => {
  showLoadingToast({});
  getDiscountsCardApi().then(async res => {
    const url = await getArrayBufferBase64(res);
    imgUrl.value = url;
    isImgModal.value = true;
    closeToast();
  });
};

const goSearch = (type: string) => {
  router.push({
    path: "/customer/pool/search",
    query: {
      type
    }
  });
};
</script>

<template>
  <div class="container">
    <div class="flex justify-around bg-white py-2">
      <div class="search-item" @click="isTagShow = true">
        <span class="inline-block truncate max-w-200px">
          {{ courseTagName || "选择标签" }}
        </span>
        <van-icon v-if="!courseTags" name="arrow-down" />
        <van-icon v-else name="close" @click.stop="clearCourseTag" />
      </div>
      <div class="search-item w-33% flex justify-between">
        <van-search
          v-model="name"
          placeholder="请输入课程名称"
          class="w-100% p-0 bg-#eef2fb!"
          style="height: 22px"
          @search="filterData"
          :clearable="false"
        />

        <van-icon name="search" />
      </div>
      <div class="search-item w-33% flex justify-between">
        <van-search
          v-model="amount"
          placeholder="请输入课程金额"
          class="w-100% p-0 bg-#eef2fb!"
          style="height: 22px"
          @search="filterData"
          :clearable="false"
        />

        <van-icon name="search" />
      </div>
    </div>
    <div class="list-content">
      <LinkList :immediate="false" ref="linkListRef" />
    </div>
    <div class="px-3 flex gap-10px">
      <van-button
        v-if="
          getAuth('telesale_admin_new_exclusiveLink_createLink') &&
          hasPermissions &&
          !loading
        "
        type="primary"
        block
        @click="addLink('addLink')"
      >
        创建动态会场链接
      </van-button>
      <van-button
        v-if="
          getAuth('telesale_admin_new_exclusiveLink_newCreateLink') &&
          hasPermissions &&
          !loading
        "
        type="primary"
        block
        @click="addLink('addNewLink')"
      >
        创建动态会场链接（新）
      </van-button>
    </div>
    <MyPicker
      v-model:show="isShow"
      v-model:value="courseType"
      :columns="typeList"
      @confirm="search"
    />
    <MyPicker
      v-model:show="isTagShow"
      v-model:value="courseTags"
      :columns="tagList"
      :options="{
        label: 'name',
        value: 'id'
      }"
      @confirm="searchTag"
    />
    <SaveImageDialog v-model:show="isImgModal" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  padding-bottom: 20px;
  box-sizing: border-box;
  :deep(.van-search) {
    height: 44px;
    padding: 0;
  }
  :deep(.van-search__content) {
    padding-left: 0;
  }
  :deep(.van-field__control) {
    background-color: #eef2fb;
    color: #3370ff !important;
    &::placeholder {
      color: #3370ff !important;
      opacity: 1;
    }
  }
  :deep(.van-search__field .van-field__left-icon) {
    display: none;
  }
  .search-item {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    background-color: #eef2fb;
    color: #3370ff;
    border-radius: 10px;
    height: 32px;
  }
  .list-content {
    flex: 1;
    padding: 24px;
    padding-top: 0;
    margin: 20px;
    background-color: #fff;
    border-radius: 20px;
    overflow: auto;
  }
}
</style>
