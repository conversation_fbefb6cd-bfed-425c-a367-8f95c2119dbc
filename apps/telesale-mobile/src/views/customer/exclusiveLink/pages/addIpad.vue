<!--
 * @Date         : 2024-04-28 12:10:32
 * @Description  : 架构ipad
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import { padLinkApi, IpadReq } from "@/api/customer/exclusiveLink";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import { ipadGoods } from "@telesale/shared";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { getArrayBufferBase64 } from "@/utils/common";

const router = useRouter();

const form = ref({
  exchange: "",
  isInstallment: 2,
  installmentPayType: ["alipayFq"]
});

const imgUrl = ref<string>("");
const show = ref<boolean>(false);
const { isStages } = storeToRefs(useUserStore());

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const onSubmit = () => {
  showLoadingToast({});
  const params: IpadReq = {
    ...form.value
  };
  params.installmentPayType = getInstallmentPayType(
    form.value.isInstallment,
    form.value.installmentPayType
  );
  Reflect.deleteProperty(params, "isInstallment");
  padLinkApi(params).then(async data => {
    const url = await getArrayBufferBase64(data);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <MySelect
          v-model:value="form.exchange"
          label="加购商品"
          placeholder="请选择加购商品"
          :columns="ipadGoods"
          :rules="[
            { required: true, message: '请选择加购商品', trigger: 'onChange' }
          ]"
        />
        <template v-if="isStages">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                direction="horizontal"
                @change="changeInstallment"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
