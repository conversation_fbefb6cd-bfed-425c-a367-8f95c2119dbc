import { http } from "@/utils/http";

export interface OrderListReq {
  workerid?: string;
  phone?: string;
  orderid?: string;
  status?: string;
  stage?: string;
  pageIndex?: number;
  pageSize?: number;
  start?: number;
  begin?: number;
  end?: number;
  isLock?: boolean;
}

export interface OrderListRes {
  id: number;
  createdAt: string;
  updatedAt: string;
  orderid: string;
  userid: string;
  status: string;
  workerid: number;
  orderCreatedAt: number;
  payTime: number;
  orderType: number;
  amount: number;
  realAmount: number;
  phone: string;
  syncType: number;
  syncStatus: number;
  department: number;
  stage: string;
  siteId: number;
  goodType: number;
  workerName: string;
  workerExten: string;
  firstValidDial: number;
  infoUuid: string;
  isPromotion: boolean;
}
// 订单列表
export const getOrderListApi = (data: OrderListReq) => {
  return http.request<ReturnList<OrderListRes>>("post", `/web/order/list`, {
    data
  });
};

export interface AppealRes {
  id: number;
  createdAt: string;
  updatedAt: string;
  orderid: string;
  workerid: number;
  voucher: string;
  status: number;
  note: string;
  amount: number;
  phone: string;
  course: string;
  onionid: string;
  reason: string;
  payTime: number;
  workerName: string;
  workerExten: string;
}

// 申诉列表
export const getAppealListApi = (data: OrderListReq) => {
  return http.request<ReturnList<AppealRes>>("post", `/web/appeal/list`, {
    data
  });
};

export interface AppealReq {
  orderid?: string;
  voucher: any[];
  note?: string;
  isB2C?: boolean;
}

// 提交申诉
export const addAppealApi = (data: AppealReq) => {
  return http.request("post", `/web/appeal/submit`, { data });
};

/**
 * @description: 申诉单是否存在
 * @param {number} orderId
 * @returns {AppealOrderRes}
 */
export const getAppealExistApi = (params: {
  orderId: string;
  excludeId?: number;
}) => {
  return http.request<{
    exist: boolean;
  }>("get", `/sync-order/appeal/exist`, {
    params
  });
};

// 获取订单信息
export const getOrderApi = (params: { orderid: string }) => {
  return http.request("get", `/web/customer/raw/order/detail`, {
    params,
    showErrorToast: false
  });
};
