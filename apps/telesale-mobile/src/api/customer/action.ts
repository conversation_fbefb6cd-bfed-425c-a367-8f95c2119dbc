import {
  CueForm,
  ExpireForm,
  GiveForm,
  PursuitForm
} from "@/types/customer/action";
import { http } from "@/utils/http";

// 赠送体验
export const giveVipApi = (data: GiveForm) => {
  return http.request("post", `/web/customer/give/vip`, {
    data
  });
};

interface GiveVip {
  userId: string;
  addDay: number;
}

/**
 * @description: 赠送体验版大会员
 * @param {GiveVip} data
 */
export const giveBigVipApi = (data: GiveVip) => {
  return http.request(
    "post",
    `wuhan-datapool/info/allocation/send_trial_big_vip`,
    {
      data
    }
  );
};

// 获取意向度字典表
export const intentionApi = (): Promise<
  {
    [x: string]: number;
  }[]
> => {
  return http.request("get", `/web/customer/intention/list`);
};

// 跟单记录-添加
export const addCallRecordApi = (data: PursuitForm) => {
  return http.request("post", `/web/customer/call/record/add`, {
    data
  });
};

// 转移线索
export const transferCustom = (data: CueForm) => {
  return http.request<{
    success: number;
    error: number;
    message: string;
  }>("post", `/web/customer/transfer`, { data });
};

// 释放，并设置冷静期
export const releaseApi = (data: ExpireForm) => {
  return http.request("post", `/web/customer/release`, {
    data
  });
};

export interface ProfileInfo {
  id: number;
  materialName: string;
  materialPath: string;
  materialFile: string;
}

export interface GiveProfile {
  userId: string;
  ids?: number[];
}

/**
 * @description: 赠送资料
 * @param {GiveProfile} data
 */
export const giveProfileApi = data => {
  return http.request<{
    successNum: number;
    failNum: number;
  }>("post", `/wuhan-datapool/materials/send`, { data });
};

/**
 * @description: 获取资料发送记录
 * @param {string} userId
 */
export const giveProfileRecordApi = (params: { userId: string }) => {
  return http.request<{
    list: {}[];
    sendRecord: {
      materialName: string;
      giftAt: number;
      giftTime: string;
      userId: string;
      workerId: number;
    }[];
  }>("get", `/wuhan-datapool/materials/send_record`, {
    params
  });
};

/**
 * @description: 获取专属资料列表
 * @returns {ProfileInfo[]} data
 */
export const getProfileApi = () => {
  return http.request<{
    list: ProfileInfo[];
  }>("get", `/wuhan-datapool/materials/list`);
};
