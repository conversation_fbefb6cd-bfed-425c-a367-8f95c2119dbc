/*
 * @Date         : 2025-02-17 14:37:39
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */
import { http } from "@/utils/http";

export interface LinkSettingReq {
  visibleType?: number;
  invisibleType?: number;
  invisibleTime: any;
  invisibleGroups?: number[];
  invisibleStart: number;
  invisibleEnd: number;
  inGroups?: boolean;
}

/**
 * @description: 获取会场链接配置可见范围
 * @returns {ArrayBuffer}
 */
export const getLinkSettingApi = () => {
  return http.request<LinkSettingReq>(
    "get",
    `/wuhan-datapool/venue_link/setting`
  );
};

/**
 * @description: 获取补差价链接配置可见范围列表
 */
export const getDiffSettingApi = () => {
  return http.request<{
    list: any[];
    infos: any[];
  }>("get", `/wuhan-datapool/difference_link/setting/list`, {
    isData: true
  });
};

/**
 * @description 获取商品列表
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/getVenueLinkGoodsList
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 */
export const getVenueLinkGoodsListApi = () => {
  return http.request(`get`, `/wuhan-datapool/listVenueLinkGoods/list`, {
    isData: true
  });
};
