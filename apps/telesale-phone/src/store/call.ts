import { defineStore } from 'pinia'
import { CallState } from '@/views/Home/types/CallState'

interface State {
  state: boolean // 在线 ｜ 离线
  callState: CallState
  callTime: number
  isActive: boolean   // 主动发起通信
  isMute: boolean // 是否静音

  phone: string
}

export const useCall = defineStore({
  id: 'CALL',
  state(): State {
    return {
      state: false,
      callState: CallState.IDLE,
      callTime: 0,
      isActive: false,
      isMute: false,

      phone: ''
    }
  },
  getters: {
    callTimeStr(state): string {
      const minutes = Math.floor(this.callTime / 60)
      const remainingSeconds = this.callTime % 60
      return `${minutes < 10 ? '0' : ''}${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
    },
    checkCanInputPhone(state): boolean {
      return this.callState === CallState.IDLE || this.callState === CallState.DISCONNECTED || import.meta.env.VITE_ENV === 'development'
    },
    isCalling(state): boolean {
      return this.callState === CallState.CALLING || this.callState === CallState.INCOMING || this.callState === CallState.CONFIRMED
    }
  },
  actions: {
  },
  persist: {
    paths: []
  }
})
