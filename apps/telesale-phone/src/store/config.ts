import { defineStore } from 'pinia'
import { invoke } from '@tauri-apps/api'
import api from '@/api'
import { useUser } from '@/store/user'

interface ConfigState {
  configSupport: boolean // 是否支持配置

  cacheChecked: boolean // 是否保存录音
  hangupChecked: boolean // 是否有挂断音
}

export const useConfig = defineStore({
  id: 'CONFIG',
  state(): ConfigState {
    return {
      configSupport: false,

      cacheChecked: false,
      hangupChecked: false,
    }
  },
  getters: {
  },
  actions: {
    getConfig() {
      invoke('handle_check_setting').then(async () => {
        const res = await api.getConfig(useUser().workerId)

        this.cacheChecked = res.cache
        this.hangupChecked = res.hang

        this.changeConfig(true)
        this.configSupport = true
      })
    },
    changeConfig(init = false) {
      console.log('changeCacheChecked', {
        is_save_audio: this.cacheChecked,
        hangup_audio_state: this.hangupChecked,
      }, init)
      invoke('handle_setting', {
        phoneSetting: {
          is_save_audio: this.cacheChecked,
          hangup_audio_state: this.hangupChecked,
        }
      }).then(resp => {
        console.log('set phone setting success!', resp)
        if (init) return
        api.setConfig({
          workerId: useUser().workerId,
          cache: this.cacheChecked,
          hang: this.hangupChecked
        })
      }).catch(err => {
        console.log('set phone setting failed: ', err.toString())
      })
    }
  },
  persist: {
    paths: []
  }
})
