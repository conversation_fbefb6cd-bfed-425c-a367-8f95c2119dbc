import { AxiosClass } from '@guanghe-pub/nexus-axios'
import { useUser } from '@/store/user'

const Axios = new AxiosClass({
  reductData: false,
  baseURL: import.meta.env.VITE_HOST_API
})

Axios.axios.interceptors.request.use((config:any) => {
  if (!config.url.includes('voucher')) {
    if (useUser().token) config.headers['Token'] = useUser().token
  }
  return config
})

Axios.axios.interceptors.response.use((res:any) => {
  return res
}, (error: any) => {
  const { response, commonErrorMsg } = error
  const { status, data } = response
  console.log('error:', error)
  if (data?.message) {
    ElMessage.error(data?.message)
  } else if (commonErrorMsg) {
    ElMessage.error(commonErrorMsg)
  }

  if (status === 401 || status === 403) tokenFailure(error)

  return Promise.reject(error)
})

export default Axios

function tokenFailure(error) {
  setTimeout(() => {
    useUser().logout()
  }, 1000)
}

