<!--
 * @Date         : 2023-11-10 14:30:15
 * @Description  : home
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="home-container">
    <Header />

    <Call :handle-call="handleCall" />
  </div>
</template>

<script lang='ts' setup>
import Header from './children/Header.vue'
import Call from './children/Call.vue'
import useCall from './hooks/useCall'
import { useUser } from '@/store/user'
import connect from './script/connect'
import { useRouter } from 'vue-router'

const { handleCall } = useCall()
const router = useRouter()
const userStore = useUser()

onBeforeMount(() => {
  if (!window.__TAURI_IPC__) {
    ElMessage.error('不在软电话系统内')
  }
})

onMounted(async () => {
  userStore.getVersion()

  if (userStore.token) {
    await userStore.getUserInfo()
    connect()
  } else {
    ElMessage.error('请重新登录')
    router.replace('/Login')
  }
})
</script>

<style scoped lang='scss'>
.home-container {
  background-color: white;
  min-height: 100vh;
  min-width: 100vw;
}
</style>
