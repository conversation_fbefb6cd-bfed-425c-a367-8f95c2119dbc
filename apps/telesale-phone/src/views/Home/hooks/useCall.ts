import { invoke, event } from '@tauri-apps/api'
import useCallTime from './useCallTime'
import { CallState } from '../types/CallState'
import { StateMessage } from '../types/StateMessage'
import { useCall } from '@/store/call'
import { useUser } from '@/store/user'

export default function () {
  const callStore = useCall()
  const userStore = useUser()

  const { startCount, endCount } = useCallTime()

  let unListen: any
  async function watchEvent() {
    unListen = await event.listen('self-event', (e: any) => {
      const ev = e.payload as StateMessage
      console.log('event is ', ev)
      if (ev.state === 'RELOAD') {
        console.log('RELOAD', ev)
        location.reload()
        return
      }

      if (ev.state === 'ONLINE') {
        callStore.state = true
      } else if (ev.state === 'OFFLINE') {
        callStore.state = false
      }

      if (ev.state === 'EXCEPTION') {
        console.log('EXCEPTION', ev)
      }


      // 在线-ONLINE、离线-OFFLINE、拨号中-CALLING、已接通-CONFIRMED、已挂断-DISCONNECTED、异常消息-EXCEPTION
      switch (ev.state) {
        case 'CALLING':
          callStore.callState = CallState.CALLING
          break
        case 'CONFIRMED':
          callStore.callState = CallState.CONFIRMED
          endCount()
          startCount()
          userStore.uploadLog()
          break
        case 'DISCONNECTED':
          callStore.phone = ''
          callStore.callState = CallState.DISCONNECTED
          callStore.isMute = false  // 每次挂断重置静音状态
          endCount()
          callStore.isActive = false
          break
      }
    })
  }

  async function handleCall(phone) {
    console.log('current state is ', callStore.callState)
    if (callStore.callState === CallState.IDLE || callStore.callState === CallState.DISCONNECTED) {
      call(phone)
    } else if (callStore.callState === CallState.CALLING || callStore.callState === CallState.CONFIRMED) {
      hangup()
    }
  }

  async function call(phone) {
    if (phone.trim().length === 0) return
    callStore.callState = CallState.CALLING

    try {
      callStore.phone = phone
      const res = await invoke('handle_call', { phoneNo: phone })
      callStore.isActive = true
      console.log('handle_call:', res)
    } catch (error) {
      console.log('handle_call err:', error)
    }
  }

  async function hangup() {
    try {
      const res = await invoke('handle_hangup')
      callStore.callState = CallState.DISCONNECTED
      console.log('resp is ', res)
    } catch (error) {
      console.log('handle_call err:', error)
    }
  }

  onBeforeMount(() => {
    watchEvent()
  })

  onUnmounted(() => {
    if (unListen) {
      unListen()
    }
  })

  return { handleCall }
}
