import { invoke } from '@tauri-apps/api'
import { useCall } from '@/store/call'

export default function() {
  const callStore = useCall()

  function handleMute() {
    let cmd = 'handle_mute'
    if (callStore.isMute) {
      cmd = 'handle_unmute'
    }

    invoke(cmd).then(resp => {
      callStore.isMute = !callStore.isMute
      console.log('resp is ', resp)
    }).catch(err => {
      console.log('err is ', err)
    })
  }

  return {
    handleMute
  }
}
