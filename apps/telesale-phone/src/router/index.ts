import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
} from 'vue-router'

import beforeEach from './guard/beforeEach'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/:catchAll(.*)',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/Login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
    meta: {
      keepAlive: false
    }
  }
]

const router = createRouter({
  history: createWebHistory('WH_CRM_v2/telesale-phone'),
  routes
})

router.beforeEach(beforeEach)

export default router
