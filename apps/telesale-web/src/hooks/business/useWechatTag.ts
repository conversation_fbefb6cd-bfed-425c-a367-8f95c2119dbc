/*
 * @Date         : 2024-06-14 18:30:02
 * @Description  : 获取企业微信tag
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ref } from "vue";
import { WeChatInfo, getWeChatListApi } from "/@/api/daily/weChatWork";
import getTags from "/@/views/system/tag/utils/getTags";

export const useWechatTag = () => {
  const wechatList = ref<WeChatInfo[]>([]);
  const allTag = ref([]);

  // 获取企微列表
  const getWechatList = async () => {
    const res = await getWeChatListApi();
    wechatList.value = res.data.list;
  };

  // 循环获取所有的企微标签
  const getAllTag = async () => {
    if (wechatList.value.length === 0) {
      await getWechatList();
    }

    const list = wechatList.value;
    for (const key of list) {
      const res = await getTags(key.id);
      allTag.value.push(...res);
    }
  };

  return {
    wechatList,
    allTag,
    getWechatList,
    getAllTag
  };
};
