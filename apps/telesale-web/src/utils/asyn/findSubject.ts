import { findSubject } from "/@/api/user";

function getSubject() {
  return findSubject()
    .then(({ data }: { data: any }) => {
      return data.subjects.map(item => {
        return item.name;
      });
    })
    .catch(() => {
      return [
        "数学",
        "物理",
        "语文",
        "化学",
        "英语",
        "生物",
        "地理",
        "自然",
        "地球",
        "实验",
        "道德与法治",
        "历史",
        "信息技术",
        "理化生实验",
        "体育与健康"
      ];
    });
}

export default getSubject;
