import { getIsInstallmentApi, getPersonMsg } from "/@/api/user";
import { useUserStore } from "/@/store/modules/user";

function getPersonMsgMath() {
  return getPersonMsg()
    .then(async ({ data }: { data: any }) => {
      data.leafNode = data.property === "manager";
      const { setStages } = useUserStore();
      try {
        const res = await getIsInstallmentApi(data.id);
        setStages(res.data.isInstallment);
      } catch (error) {
        setStages(false);
      }
      return data;
    })
    .catch(() => {
      return {};
    });
}

export default getPersonMsgMath;
