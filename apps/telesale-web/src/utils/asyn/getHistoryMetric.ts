import { metric } from "/@/api/user";
import { useUserStoreHook } from "/@/store/modules/user";

function getHistoryMetric() {
  const { authorizationMap } = useUserStoreHook();
  const auth = authorizationMap.includes("telesale_admin_clue_distribute");
  if (auth) {
    return metric().then(({ data }: { data: any }) => {
      useUserStoreHook().setHistoryMetric(data);
    });
  }
}

export default getHistoryMetric;
