const Layout = () => import("/@/layout/index.vue");

const activeRouter = {
  path: "/active",
  component: Layout,
  redirect: "noRedirect",
  meta: {
    title: "活动管理"
  },
  children: [
    {
      path: "/active/transfer/index",
      name: "transfer",
      component: () => import("/@/views/active/transfer/index.vue"),
      meta: {
        title: "转介绍",
        keepAlive: true
      },
      children: [
        {
          path: "/active/transferDetails/index",
          name: "transferDetails",
          component: () => import("/@/views/active/transferDetails/index.vue"),
          meta: {
            title: "转介绍详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/channel/index",
      name: "channel",
      component: () => import("/@/views/active/channel/index.vue"),
      meta: {
        title: "渠道活码",
        keepAlive: true
      },
      children: [
        {
          path: "/active/channelDetails/index",
          name: "channelDetails",
          component: () => import("/@/views/active/channelDetails/index.vue"),
          meta: {
            title: "渠道活码详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/channelType/index",
      name: "channelType",
      component: () => import("/@/views/active/channelType/index.vue"),
      meta: {
        title: "活码类型管理",
        keepAlive: true
      }
    },
    {
      path: "/active/ruleConfig/index",
      name: "ruleConfig",
      component: () => import("/@/views/active/ruleConfig/index.vue"),
      meta: {
        title: "活码分配规则",
        keepAlive: true
      },
      children: [
        {
          path: "/active/ruleConfigDetails/index",
          name: "ruleConfigDetails",
          component: () =>
            import("/@/views/active/ruleConfigDetails/index.vue"),
          meta: {
            title: "活码分配规则详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/uiConfig/index",
      name: "uiConfig",
      component: () => import("/@/views/active/uiConfig/index.vue"),
      meta: {
        title: "活码配置",
        keepAlive: true
      },
      children: [
        {
          path: "/active/uiConfigDetails/index",
          name: "uiConfigDetails",
          component: () => import("/@/views/active/uiConfigDetails/index.vue"),
          meta: {
            title: "活码配置详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/delivery/index",
      name: "Delivery",
      component: () => import("/@/views/active/delivery/index.vue"),
      meta: {
        title: "发货管理",
        keepAlive: true
      }
    },
    {
      path: "/active/welcomeMessage/index",
      name: "WelcomeMessage",
      component: () => import("/@/views/active/welcomeMessage/index.vue"),
      meta: {
        title: "分时段欢迎语"
      },
      children: [
        {
          path: "/active/welcomeDetail/index",
          name: "WelcomeDetail",
          component: () => import("/@/views/active/welcomeDetail/index.vue"),
          meta: {
            title: "查看欢迎语",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/themeConfig/index",
      name: "ThemeConfig",
      component: () => import("/@/views/active/themeConfig/index.vue"),
      meta: {
        title: "引流页主题配置"
      },
      children: [
        {
          path: "/active/themeConfigDetails",
          name: "ThemeConfigDetails",
          component: () =>
            import("/@/views/active/themeConfigDetails/index.vue"),
          meta: {
            title: "引流页主题配置详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/active/videoNumber/index",
      name: "videoNumber",
      component: () => import("/@/views/active/videoNumber/index.vue"),
      meta: {
        title: "视频号素材"
      },
      children: [
        {
          path: "/active/videoNumberDetails/index",
          name: "videoNumberDetails",
          component: () =>
            import("/@/views/active/videoNumberDetails/index.vue"),
          meta: {
            title: "视频号素材详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    }
  ]
};

export default activeRouter;
