/*
 * @Date         : 2024-07-18 10:58:08
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { ElMessage } from "element-plus";
import type { Directive, DirectiveBinding } from "vue";

function addEventListener(el: Element, binding: DirectiveBinding) {
  const { value } = binding;

  el.setAttribute("copyValue", String(value));

  const copyHandler = (): void => {
    navigator.clipboard
      .writeText(el.getAttribute("copyValue") || "")
      .then(() => {
        ElMessage.success("复制成功！");
      })
      .catch(() => {
        ElMessage.error("复制失败！");
      });
  };

  el.addEventListener("click", copyHandler);
}

export const copy: Directive = {
  mounted(el: HTMLElement, binding) {
    addEventListener(el, binding);
  },
  updated(el: HTMLElement, binding) {
    const { value } = binding;
    el.setAttribute("copyValue", String(value));
  }
};
