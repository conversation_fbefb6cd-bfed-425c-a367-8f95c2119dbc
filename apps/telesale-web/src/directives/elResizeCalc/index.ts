import { Directive } from "vue";
import type { DirectiveBinding, VNode } from "vue";
import elementResizeDetectorMaker from "element-resize-detector";
import type { Erd } from "element-resize-detector";
import { emitter } from "/@/utils/mitt";

const erd: Erd = elementResizeDetectorMaker({
  strategy: "object"
});

export const resizeCalc: Directive = {
  mounted(el: HTMLElement, binding?: DirectiveBinding, vnode?: VNode) {
    erd.listenTo(el.parentElement.parentElement, elem => {
      let width = 1000;
      let height = 625;
      const findY = parseInt(
        window?.getComputedStyle(
          document.querySelector(".main-container .el-form")
        )?.height || 0
      );
      height = elem.offsetHeight - findY - 82;
      width = elem.offsetWidth - 80;
      if (binding?.instance) {
        emitter.emit("resizeCalc", { detail: { width, height } });
      } else {
        vnode.el.dispatchEvent(
          new CustomEvent("resizeCalc", { detail: { width, height } })
        );
      }
    });
  },
  unmounted(el: HTMLElement) {
    erd.uninstall(el.parentElement.parentElement);
  }
};
