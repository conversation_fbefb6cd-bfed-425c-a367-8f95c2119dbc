<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "/@/store/modules/user";
import { useAppStoreHook } from "/@/store/modules/app";
import { getCallRecord, lookPhone } from "/@/api/daily";
import paramsHandle from "/@/utils/handle/paramsHandle";
import durationChange from "/@/utils/handle/durationChange";
import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./components/Search.vue";
import channelMath from "/@/utils/asyn/findChannel";
import AudioMsg from "./dialog/AudioMsg.vue";
import stateList from "./utils/stateList";
import useHistory from "/@/views/customer/details/AIRobot/hooks/useHistory";
import useGetCurrentState from "/@/views/customer/details/AIRobot/hooks/useGetCurrentState";
import { phoneTypeMap } from "/@/utils/const/customer";
import { getAuth } from "/@/utils/auth";

interface Props {
  method?: string;
  phone?: string;
  type?: string;
  workerId?: number;
  infoUuid?: string;
  fromCall?: boolean;
  familyId?: string;
}

const props = defineProps<Props>();

let device = useAppStoreHook().device;

let isShowPage = ref(true);

function durationChangeMath(row) {
  return durationChange(row["call_time_length"]);
}

function phoneChange(row) {
  if (row["province"] === row["district"]) {
    return row["province"];
  }
  return row["province"] + row["district"];
}

const isFamily = getAuth("telesale_admin_customer_family_detail");

let listHeader: any = ref([
  {
    field: "action_id",
    desc: "通话ID",
    minWidth: 155,
    isCopy: true,
    fixed: true
  },
  {
    field: "familyId",
    desc: "家庭ID",
    minWidth: 115,
    isFamily: true,
    isCopy: true,
    fixed: true,
    addType: "up",
    isShow: () => !props.familyId && isFamily
  },
  { field: "onionId", desc: "洋葱ID", isShow: !!props.familyId, minWidth: 110 },
  {
    field: "called_no",
    desc: "客户手机号",
    minWidth: 115,
    fixed: true
  },
  {
    field: "phoneType",
    desc: "手机号类型",
    isShow: !!props.familyId,
    minWidth: 130,
    customRender: ({ text }) => {
      return phoneTypeMap[text];
    }
  },
  {
    field: "province",
    desc: "号码归属地",
    filters: phoneChange,
    minWidth: 95
  },
  {
    field: "createdAt",
    desc: "创建时间",
    timeChange: isShowPage.value ? 3 : 2,
    minWidth: 115
  },
  { field: "begin", desc: "呼出时间", timeChange: 2, minWidth: 115 },
  { field: "end", desc: "挂断时间", timeChange: 2, minWidth: 115 },
  {
    field: "call_time_length",
    desc: "通话时长",
    minWidth: 100,
    filters: durationChangeMath
  },
  { field: "state", desc: "呼叫状态", minWidth: 90, typeChange: stateList },
  {
    field: "hangUpPart",
    desc: "挂断方",
    minWidth: 70,
    filters: row =>
      row.hangUpPart === "agent"
        ? "坐席"
        : row.hangUpPart === "customer"
        ? "客户"
        : ""
  }
]);

const drawerData = [
  {
    field: "called_no",
    desc: "客户手机号",
    minWidth: 115
  },
  { field: "begin", desc: "呼出时间", timeChange: 2, minWidth: 135 },
  {
    field: "call_time_length",
    desc: "通话时长",
    filters: durationChangeMath,
    minWidth: 90
  },
  { field: "state", desc: "呼叫状态", typeChange: stateList, minWidth: 90 }
];

const agent = [
  {
    field: "workerName",
    desc: "坐席名称",
    minWidth: 100,
    idTransfer: "name"
  },
  {
    field: "cn",
    desc: "外呼渠道",
    minWidth: 100,
    filtersList: useUserStoreHook().channelList
  },
  {
    field: "exten",
    desc: "外呼工号",
    minWidth: 105
  }
];

function getPhone() {
  if (!formRefs.value.form.action_id) {
    ElMessage.warning("请输入通话ID");
    return;
  }
  isShowPage.value = false;
  loading.value = true;
  lookPhone(formRefs.value.form.action_id)
    .then(({ data }: { data: any }) => {
      if (data.actionId) {
        dataList.value = [
          {
            ...data,
            action_id: data.actionId,
            called_no: data.calledNo,
            call_time_length: data.callTimeLength,
            workerid: data.workerId,
            agent_name: data.agentName,
            file_server: data.fileServer,
            record_file: data.recordFile,
            createdAt:
              !data.createdAt || data.createdAt === "0001-01-01T00:00:00Z"
                ? ""
                : new Date(data.createdAt).getTime()
          }
        ];
      } else {
        dataList.value = [];
      }
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      loading.value = false;
    });
}

const operation = [
  {
    event: "look",
    text: "查看录音",
    isShow: row => row.call_time_length
  },
  {
    event: "lookAIRobot",
    text: "对话转译",
    isShow: row =>
      row.call_time_length &&
      device !== "mobile" &&
      props.fromCall &&
      useUserStoreHook().checkAuth("telesale_show_ai_call") &&
      row.call_time_length >= 10
  }
];
//带分页列表数据必备
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

//分页
const rePaginationRefs = ref();
function onSearch(val = false) {
  val === true && (rePaginationRefs.value.pageSize = 20);
  rePaginationRefs.value.onSearch();
}

//表头筛选
function filterChange(row) {
  row.cn !== undefined && (formRefs.value.form.channelId = row.cn[0] || 0);
  onSearch();
}

//表头重置
const tableRefs = ref();
function resetFitler() {
  tableRefs.value.resetFilter();
}

//form查询
const formRefs = ref();
function getList() {
  if (props.method === "agent" && !props.infoUuid && !props.familyId) return;
  if (formRefs.value.form.durationStart > formRefs.value.form.durationEnd) {
    ElMessage.warning("通话时长最大值不能小于最小值");
    return;
  }
  isShowPage.value = true;
  loading.value = true;

  let params = paramsHandle(formRefs.value.form, {
    time: true,
    string: ["workerid"],
    zero: ["durationStart", "durationEnd", "orgId"],
    pageIndex: rePaginationRefs.value.pageIndex,
    pageSize: rePaginationRefs.value.pageSize
  });
  if (props.method !== "admin") {
    params.lastSixMonth = true;
    params.workerid = props.workerId + "";
  } else {
    params.lastSixMonth = false;
  }

  if (props.infoUuid) {
    params.infoUuid = props.infoUuid;
  }

  if (props.familyId) {
    params.familyId = props.familyId;
    params.workerid = undefined;
  }

  getCallRecord(params)
    .then(({ data }: { data: any }) => {
      dataList.value = data.list || [];
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}

function fromWays() {
  //是不是从客户管理那边打开 ===agent为是
  if (props.method === "agent") {
    // formRefs.value.form.callNo = props.phone;
    if (props.type === "drawer") {
      listHeader.value.splice(0, 9, ...drawerData);
    } else {
      listHeader.value.splice(9, 0, ...agent);
    }
    if (props.infoUuid || props.familyId) {
      onSearch();
    }
  } else {
    listHeader.value.splice(9, 0, ...agent);
    onSearch();
  }
}

//弹窗
const isModel = ref<boolean>(false);
const memeryData = ref();

function look(row) {
  if (!row.record_file) {
    ElMessage.warning("此呼叫记录不存在录音");
    return;
  }
  memeryData.value = row;
  isModel.value = true;
}

const { getHistory } = useHistory(props.infoUuid);
const { robotState } = useGetCurrentState();
const activeTab = inject("activeTab", ref(""));
function parantMath({ key, params }) {
  switch (key) {
    case "look":
      look(params);
      break;
    case "lookAIRobot":
      console.log(params);
      getHistory(params.action_id);
      activeTab.value = "AI辅助";
      break;
  }
}

onMounted(async () => {
  if (!useUserStoreHook().channelList.length) {
    useUserStoreHook().setChannelList(await channelMath());
  }
  fromWays();
});
</script>

<template>
  <div v-loading="loading">
    <Search
      v-show="method === 'admin'"
      ref="formRefs"
      @onSearch="onSearch"
      @getPhone="getPhone"
      @resetFitler="resetFitler"
      :method="method"
    />
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :filterChange="filterChange"
        :operation="operation"
        @parantMath="parantMath"
      />
      <template v-else>
        <ReCardList
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
        />
      </template>
    </div>
    <RePagination
      v-show="type !== 'drawer' && isShowPage"
      ref="rePaginationRefs"
      :total="total"
      @getList="getList"
    />
    <AudioMsg v-if="isModel" v-model:value="isModel" :memeryData="memeryData" />
  </div>
</template>
