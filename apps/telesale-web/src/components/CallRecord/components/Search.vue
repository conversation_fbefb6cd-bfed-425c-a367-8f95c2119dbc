<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "/@/store/modules/user";
import FormOrgAgent from "/@/components/FormOrgAgent/index.vue";
import dayjs from "dayjs";
import {
  disabledDate,
  clearDate,
  onPick
} from "/@/utils/handle/timeSelectLimit";

interface Props {
  method: string;
}

interface Emits {
  (e: "onSearch", val: boolean): void;
  (e: "getPhone"): void;
  (e: "resetFitler"): void;
}

withDefaults(defineProps<Props>(), {});
const emit = defineEmits<Emits>();

function onSearch(val = false) {
  emit("onSearch", val);
}

function getPhone() {
  emit("getPhone");
}

//form查询
const form = reactive({
  time: [dayjs().startOf("month").valueOf(), dayjs().endOf("date").valueOf()],
  exten: "",
  callNo: "",
  workerid: undefined,
  durationStart: undefined,
  durationEnd: undefined,
  orgId: "",
  channelId: 0,
  action_id: ""
});

const formRef = ref<FormInstance>();
const formOrgAgentRef = ref();

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formOrgAgentRef.value.agentListReset();
  formEl.resetFields();
  form.exten = "";
  form.channelId = 0;
  form.durationEnd = undefined;
  emit("resetFitler");
  onSearch(true);
};

defineExpose({
  form
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
    <template v-if="method === 'admin'">
      <FormOrgAgent
        ref="formOrgAgentRef"
        limitName="telesale_admin_call_group"
        workerId="workerid"
        v-model:form="form"
        agentListKey="allAgentList"
        :isAllOrg="true"
        :isAloneAgent="true"
      />

      <el-form-item prop="action_id">
        <el-input
          v-model.trim="form.action_id"
          placeholder="请输入通话ID"
          clearable
          @keyup.enter="onSearch"
        >
          <template
            #append
            v-if="
              useUserStoreHook().authorizationMap.indexOf(
                'telesale_admin_call_phone_look'
              ) > -1
            "
          >
            <el-button @click="getPhone">
              <IconifyIconOffline icon="search" />
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="callNo">
        <el-input
          v-model.trim="form.callNo"
          placeholder="请输入客户手机号"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item prop="durationStart">
        <el-input-number
          v-model="form.durationStart"
          :min="0"
          :step="1"
          step-strictly
          @keyup.enter="onSearch"
          placeholder="最小通话时长/s"
          :controls="false"
        />
        至
        <el-input-number
          v-model="form.durationEnd"
          :min="0"
          :step="1"
          step-strictly
          placeholder="最大通话时长/s"
          @keyup.enter="onSearch"
          :controls="false"
        />
      </el-form-item>
      <el-form-item prop="time">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          value-format="x"
          range-separator="至"
          start-placeholder="创建时间-开始"
          end-placeholder="创建时间-结束"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
          :disabled-date="disabledDate"
          @visible-change="clearDate"
          @calendar-change="onPick"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </template>
  </el-form>
</template>
