<script lang="ts" setup>
import { computed, ref } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import timeChange from "/@/utils/handle/timeChange";
import ReAudio from "/@/components/ReAudio/index.vue";
import { downloadCallRecord } from "/@/api/daily";
import downloadFile from "/@/utils/handle/downloadFile";

let device = useAppStoreHook().device;

interface Props {
  value: boolean;
  memeryData: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

let loading = ref<Boolean>(false);

let msg = ref([
  { title: "客户手机号", value: props.memeryData.called_no },
  { title: "坐席名称", value: props.memeryData.agent_name },
  { title: "呼出时间", value: timeChange(props.memeryData.begin, 2) },
  { title: "坐席工号", value: props.memeryData.exten }
]);

let audioSrc = ref(
  props.memeryData.file_server + "/" + props.memeryData.record_file
);

function downloadAudio(val) {
  if (val) {
    return;
  }
  loading.value = true;
  downloadCallRecord({ url: audioSrc.value })
    .then(() => {
      downloadFile(audioSrc.value, true);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
</script>

<template>
  <el-dialog
    title="查看录音"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
  >
    <div v-loading="loading">
      <el-descriptions :column="2" direction="vertical" :border="true">
        <el-descriptions-item
          v-for="(item, index) in msg"
          :label="item.title"
          :key="index"
        >
          {{ item.value }}
        </el-descriptions-item>
      </el-descriptions>
      <audio
        v-if="device === 'mobile'"
        :src="audioSrc"
        controls
        style="margin: 20px auto 10px; display: block; width: 250px"
      >
        您的浏览器不支持 audio 标签。
      </audio>
      <ReAudio v-else :audioSrc="audioSrc" @downloadAudio="downloadAudio" />
    </div>
    <template #footer>
      <el-button @click="handleClose">返回</el-button>
    </template>
  </el-dialog>
</template>
