<!--
 * @Date         : 2024-10-30 15:58:42
 * @Description  : 省市区选择器
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useCommonDataStore } from "/@/store/modules/commonData";
import { CityValue } from "./types";
import { CascaderInstance } from "element-plus";

const props = withDefaults(
  defineProps<{
    value: CityValue[];
    level?: number;
    options?: {
      provinceKey?: string;
      cityKey?: string;
    };
  }>(),
  {
    options: () => {
      return {
        provinceKey: "province",
        cityKey: "city"
      };
    }
  }
);

const emits = defineEmits(["update:value"]);

const commonData = useCommonDataStore();
const { getCityData } = commonData;
const { cityData } = storeToRefs(commonData);
const cityValue = ref();
const cascaderRef = ref<CascaderInstance>();
const optionsValue = computed(() => {
  const list = cityData.value.map(province => {
    return {
      value: province.code,
      label: province.name,
      children: province.children.map(city => {
        return {
          value: city.code,
          label: city.name
        };
      })
    };
  });
  return list;
});
const loading = ref(false);

const changeCity = (e: any) => {
  const result = e.reduce((acc, [province, city]) => {
    const currentProvince = acc.find(
      item => item[props.options.provinceKey] === province
    );
    if (!currentProvince) {
      acc.push({
        [props.options.provinceKey]: province,
        [props.options.cityKey]: [city]
      });
    } else {
      currentProvince[props.options.cityKey].push(city);
    }
    return acc;
  }, []);
  emits("update:value", result);
};

const clear = () => {
  // cascaderRef.value?.cascaderPanelRef?.clearCheckedNodes();
  // emits("update:value", []);
};

onMounted(() => {
  if (cityData.value?.length === 0) {
    loading.value = true;
    getCityData().finally(() => {
      loading.value = false;
    });
  }
});

watch(
  () => props.value,
  n => {
    const list = [];
    n?.forEach(item => {
      const res = item[props.options.cityKey]?.map(city => {
        return [item[props.options.provinceKey], city];
      });
      list.push(...res);
    });
    cityValue.value = list;
  },
  { immediate: true }
);

defineExpose({
  clear
});
</script>

<template>
  <div v-loading="loading">
    <el-cascader
      ref="cascaderRef"
      :model-value="cityValue"
      :options="optionsValue"
      placeholder="请选择地区"
      :props="{
        multiple: true
      }"
      collapse-tags
      collapse-tags-tooltip
      clearable
      @change="changeCity"
    />
  </div>
</template>

<style lang="scss" scoped></style>
