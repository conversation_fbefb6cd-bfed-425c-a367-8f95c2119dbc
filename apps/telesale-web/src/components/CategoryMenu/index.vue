<script lang="ts" setup>
const props = defineProps<{
  currentId?: number;
  dataList: any[];
  name: string;
  loading?: boolean;
  isCollapse?: boolean;
  dropdownOptions: {
    isShow?: Function;
    columns: {
      label: string;
      onClick: Function;
      isShow?: Function;
    }[];
  };
}>();
const emits = defineEmits(["update:currentId"]);

const currentId = computed({
  get() {
    return props.currentId;
  },
  set(val: number) {
    emits("update:currentId", val);
  }
});

const isShowHandle = ref(false);

const handleClick = (id: number) => {
  currentId.value = id;
};

const getShowDropItem = (drop: any, item: any) => {
  return drop.isShow ? drop.isShow(item) : true;
};

const changeDropDown = (val: boolean, item) => {
  isShowHandle.value = val;
  if (!val) {
    setTimeout(() => {
      item.showHandle = false;
    }, 200);
  }
};

const mouseoverFn = (item: any) => {
  if (isShowHandle.value) return;
  item.showHandle = true;
};

const mouseleaveFn = (item: any) => {
  if (isShowHandle.value) return;
  item.showHandle = false;
};
</script>

<template>
  <div>
    <!-- 头部 -->
    <slot name="header" />

    <!-- 内容部分 -->
    <div
      class="mt-10px overflow-y-auto relative"
      :class="{ 'min-h-300px': !props.isCollapse }"
      v-loading="loading"
    >
      <div class="w-full h-full absolute" v-if="isShowHandle" />
      <div
        v-for="(item, index) in props.dataList"
        :key="index"
        class="flex justify-between items-center p-5px hover:bg-[#afe4fd] cursor-pointer mt-5px w-full relative"
        :class="{
          'bg-[#afe4fd]': currentId && currentId === item.id,
          'mb-20px': index === props.dataList.length - 1
        }"
        @mouseover="mouseoverFn(item)"
        @mouseleave="mouseleaveFn(item)"
        @click="handleClick(item.id)"
      >
        <div class="flex items-center justify-between w-[calc(100%-20px)]">
          <div class="ml-4px w-[calc(100%-2px)] truncate">
            <el-tooltip
              :content="item[props.name]"
              placement="top"
              effect="dark"
            >
              {{ item[props.name] }}
            </el-tooltip>
          </div>
        </div>
        <el-dropdown
          trigger="click"
          v-show="item.showHandle"
          :teleported="props.isCollapse"
          @visible-change="changeDropDown($event, item)"
          v-if="
            props.dropdownOptions.isShow
              ? props.dropdownOptions.isShow(item)
              : true
          "
        >
          <div @click.stop>...</div>
          <template #dropdown>
            <el-dropdown-menu>
              <template
                v-for="drop in props.dropdownOptions.columns"
                :key="drop.label"
              >
                <el-dropdown-item
                  v-if="getShowDropItem(drop, item)"
                  @click="drop.onClick(item)"
                >
                  {{ drop.label }}
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
