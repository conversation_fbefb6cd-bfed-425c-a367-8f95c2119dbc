<script setup lang="ts">
import { nextTick, ref, computed } from "vue";
import { ElMessage, ElTable } from "element-plus";
import { cloneDeep } from "lodash-es";
import { TableObj, OperationObj } from "./types";
import { useUserStoreHook } from "/@/store/modules/user";
import typeChange from "/@/utils/handle/typeChange";
import timeChange from "/@/utils/handle/timeChange";
import HelpHint from "../HelpHint/index.vue";
import { DocumentCopy } from "@element-plus/icons-vue";
import { useClipboard } from "@vueuse/core";
import FamilyModal from "/@/views/customer/components/FamilyModal/index.vue";
import { getAuth } from "/@/utils/auth";

interface Props {
  rowKey?: string; // row-key
  dataList: any[]; //显示的数据
  listHeader: TableObj[]; //表头=>Table-column 属性
  showSummary?: boolean; //是否在表尾显示合计行  为了绕过ele的bug，此值为是，表格border为false
  selection?: boolean; //是否多选
  indexCode?: boolean; //是否有序号
  summaryMethod?: any; //showSummary为true时自定义的合计计算方法
  filterChange?: any; //表头筛选
  operation?: OperationObj[]; //表格操作按钮列表
  widthOperation?: number; //操作栏宽度
  maxHeight?: number; //最大高度
  sortChange?: any; //当表格的排序条件发生变化的时候会触发该事件
  description?: string; //description
  defaultExpandAll?: boolean; // 是否展开树形结构，默认展开
  defaultSort?: {
    prop: string;
    order: "ascending" | "descending";
  };
}

//默认值
const props = withDefaults(defineProps<Props>(), {
  dataList: () => [],
  listHeader: () => [],
  operation: () => [],
  showSummary: () => false,
  selection: () => false,
  indexCode: () => false,
  summaryMethod: null,
  filterChange: null,
  widthOperation: 100,
  sortChange: null,
  description: "暂无数据",
  defaultExpandAll: true
});

const { copy } = useClipboard();

const familyId = ref<string>();
const userId = ref<string>();
const isFamilyModal = ref<boolean>(false);

const columns = computed(() => {
  return cloneDeep(props.listHeader);
});
function formatter(row, item) {
  if (item.typeChange) {
    //处理一些id转换成name
    return typeChange(row[item.field], item.typeChange);
  } else if (item.idTransfer) {
    //坐席相关的字段内容转换
    return idTransfer(item.idTransfer, row[item.idName || "workerid"]);
  } else if (item.filters) {
    //filters代表自定义函数
    return item.filters(row, item.field);
  } else if (item.timeChange) {
    //时间转换
    return timeChange(row[item.field], item.timeChange);
  } else {
    return row[item.field];
  }
}

function idTransfer(key, id) {
  let item = useUserStoreHook().allAgentObj[id];
  return item ? item[key] : key === "name" ? id : "";
}

const emit = defineEmits(["parantMath", "filterHeadData"]);

function click(key, params, fn = null) {
  if (!key) {
    return fn?.(params);
  }
  emit("parantMath", { key, params });
}

const tableRef = ref<InstanceType<typeof ElTable>>();
const helpHintRefs = ref([]);

const helpHintRefsFn = (el, field) => {
  if (helpHintRefs.value.some(item => item.field === field)) return;
  helpHintRefs.value.push({
    el,
    field
  });
};

function setCurrent(index) {
  nextTick(() => {
    //目前只支持单选表格高亮回显
    tableRef.value!.setCurrentRow(props.dataList[index]);
  });
}

let currentRow = null;
function rowClick(row) {
  currentRow = row;
}
function getClickRow() {
  return currentRow;
}
function handleSelectionChange() {
  return tableRef.value.getSelectionRows();
}
function resetFilter() {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  tableRef.value?.clearFilter();
  columns.value.forEach((item, index) => {
    item.filteredValue = props.listHeader[index].filteredValue;
  });

  helpHintRefs.value?.forEach(item => {
    item?.el.resetFilterData?.();
  });
}

const clearFilterData = () => {
  helpHintRefs.value?.forEach(item => {
    item?.el.clearFilterData?.();
  });
};

function clearSort() {
  return tableRef.value.clearSort();
}

const filterData = e => {
  const list = Object.keys(e);
  columns.value.forEach(item => {
    if (list.includes(item.field)) {
      item.filteredValue = e[item.field][0];
    }
  });
  return props.filterChange(e);
};

const openFamily = (row: any) => {
  if (!getAuth("telesale_admin_customer_family_detail")) return;
  familyId.value = row.familyId;
  userId.value = row.userid || row.userId;
  isFamilyModal.value = true;
};

const copyText = (e: Event) => {
  // 假设 elem 是你当前的元素
  const elem = e.currentTarget as HTMLElement;
  // 获取父元素
  const parent = elem.parentNode as HTMLElement;
  // 获取所有子元素
  const children = parent.childNodes;
  // 初始化一个字符串来存储所有文本
  let allText = "";
  // 遍历子节点，把文本节点的内容加到 allText 中
  children.forEach((child: Node) => {
    if (child.nodeType === Node.TEXT_NODE) {
      allText += child.textContent;
    }
    // 如果子节点还有子节点，递归遍历
    else if (child.hasChildNodes()) {
      allText += (child as HTMLElement).innerText;
    }
  });
  copy(allText);
  ElMessage.success("复制成功");
};

const filterHeadData = (val: Record<string, any>) => {
  emit("filterHeadData", val);
};

const sort = data => {
  return tableRef.value.sort(data.prop, data.order);
};

defineExpose({
  setCurrent,
  getClickRow,
  resetFilter,
  handleSelectionChange,
  clearSort,
  sort,
  clearFilterData
});
</script>
<template>
  <el-table
    ref="tableRef"
    v-horizontal-scroll
    highlight-current-row
    :default-expand-all="props.defaultExpandAll"
    :row-key="props.rowKey"
    :defaultSort="props.defaultSort"
    stripe
    :border="true"
    :data="props.dataList"
    :header-cell-style="{ background: '#fafafa', color: '#606266' }"
    @filter-change="filterData"
    :max-height="maxHeight"
    :show-summary="showSummary"
    :summary-method="summaryMethod"
    @row-click="rowClick"
    @sort-change="sortChange"
    :tooltip-options="{ popperClass: 'g-tooltip' }"
  >
    <template #empty><el-empty :description="description" /></template>
    <!--      加序号-->
    <el-table-column type="index" width="60" fixed v-if="indexCode" />
    <!--        是否多选-->
    <el-table-column type="selection" width="40" fixed v-if="selection" />
    <template v-for="item in columns" :key="item.field">
      <el-table-column
        v-if="
          item.isShow === undefined ||
          item.isShow === true ||
          (typeof item.isShow === 'function' && item.isShow(item))
        "
        :label="item.desc"
        :sortable="item.sortable ? 'custom' : false"
        :prop="item.field"
        :min-width="item.minWidth"
        :show-overflow-tooltip="item.showTip"
        :column-key="item.field"
        :filters="item.filtersList"
        :filter-multiple="false"
        :filtered-value="item.filteredValue ? [item.filteredValue] : []"
        :fixed="item.fixed"
      >
        <template #header>
          <template v-if="item.filterOptions || item.filterCascaderOptions">
            <HelpHint
              :key="item.field + 'filter'"
              :ref="el => helpHintRefsFn(el, item.field)"
              :item="item"
              @filterHeadData="filterHeadData"
            />
          </template>
          <template v-else>
            <HelpHint :item="item" />
          </template>
        </template>
        <template #default="scope">
          <div v-if="item.isFamily" class="flex">
            <el-tooltip :content="scope.row[item.field]" class="w-100%">
              <div
                class="w-[calc(100%-20px)] truncate cursor-pointer"
                :class="{
                  'c-#409eff': getAuth('telesale_admin_customer_family_detail')
                }"
                @click="openFamily(scope.row)"
              >
                {{ scope.row[item.field] }}
              </div>
            </el-tooltip>
            <span
              v-if="scope.row[item.field]"
              @click="copyText"
              class="w-min-14px h-20px ml-5px cursor-pointer"
            >
              <el-icon>
                <DocumentCopy />
              </el-icon>
            </span>
          </div>
          <div
            class="flex items-center flex-nowrap justify-center"
            :class="{ 'w-100% truncate inline-block!': item.showTip }"
            v-else
          >
            <div
              class="g-text-btn clickBtn"
              v-if="item.event"
              @click="click(item.event, scope.row)"
            >
              {{ scope.row[item.field] }}
            </div>
            <!-- render -->
            <template v-else-if="item.customRender">
              <component
                :is="item.customRender"
                :row="scope.row"
                :index="scope.$index"
                :text="scope.row[item.field]"
                :column="item"
              />
            </template>
            <!-- slot -->
            <div v-else-if="item.slot?.name" class="w-100%">
              <slot
                :name="item.slot.name"
                :row="scope.row"
                :index="scope.$index"
                :text="scope.row[item.field]"
                :column="item"
              />
            </div>
            <!--  挂载含html的字符串-->
            <span v-else-if="item.htmlChange" v-html="scope.row[item.field]" />
            <!--涉及到样式变化 -->
            <span
              v-else
              :style="
                item.eleSet &&
                scope.row[item.eleSet.referProp || item.field] ===
                  item.eleSet.val
                  ? item.eleSet.sty
                  : ''
              "
            >
              {{ formatter(scope.row, item) }}
            </span>
            <span
              v-if="item.isCopy && scope.row[item.field]"
              @click="copyText"
              class="w-14px h-20px ml-5px cursor-pointer"
            >
              <el-icon>
                <DocumentCopy />
              </el-icon>
            </span>
          </div>
          <!--是否有事件-->
        </template>
      </el-table-column>
    </template>
    <slot name="appendColumn" />
    <el-table-column
      fixed="right"
      label="操作"
      :width="props.widthOperation"
      v-if="props.operation.length"
    >
      <template #default="scope">
        <template v-for="item in operation" :key="item.event">
          <!-- isShow代表按钮显示隐藏功能 -->
          <template
            v-if="
              item.isShow === undefined ||
              item.isShow === true ||
              (typeof item.isShow === 'function' && item.isShow(scope.row))
            "
          >
            <el-popconfirm
              :title="item.popconfirm"
              @confirm="click(item.event, scope.row, item.eventFn)"
              v-if="item.popconfirm"
            >
              <template #reference>
                <el-button link type="primary">
                  {{ item.text }}
                </el-button>
              </template>
            </el-popconfirm>
            <div v-else-if="item.customRender">
              <component
                :is="item.customRender"
                :row="scope.row"
                :index="scope.$index"
                :column="item"
              />
            </div>
            <!-- slot -->
            <div v-else-if="item.slot?.name">
              <slot
                :name="item.slot.name"
                :row="scope.row"
                :index="scope.$index"
                :column="item"
              />
            </div>
            <el-button
              type="primary"
              link
              v-else
              @click="click(item.event, scope.row, item.eventFn)"
            >
              {{ item.text }}
            </el-button>
          </template>
        </template>
      </template>
    </el-table-column>
  </el-table>
  <FamilyModal
    v-if="isFamilyModal"
    v-model:value="isFamilyModal"
    :familyId="familyId"
    :userId="userId"
  />
</template>

<style lang="scss" scoped>
//滚动条 滑动时的背景颜色
::v-deep(.el-scrollbar__thumb) {
  background-color: blue;
}
//鼠标悬停滚动条的大小
::v-deep(.el-table-horizontal-scrollbar:hover) {
  transform: scaleY(1.5);
  transform: scaleY(1.75) translateY(-10%);
}
</style>
