<script setup lang="ts" name="CommonContext">
import { computed } from "vue";
interface Props {
  activeForm: any;
  state: string;
  type: string;
}
interface Emits {
  (e: "update:activeForm", val: any): void;
  (e: "update:state", val: string): void;
  (e: "stateChange"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const activeForm = computed({
  get() {
    return props.activeForm;
  },
  set(val: any) {
    emit("update:activeForm", val);
  }
});
const state = computed({
  get() {
    return props.state;
  },
  set(val: string) {
    emit("update:state", val);
  }
});

function stateChange() {
  emit("stateChange");
}
let styleList = [
  {
    key: "changeLine",
    name: "换行"
  },
  {
    key: "italics",
    name: "斜体"
  },
  {
    key: "bold",
    name: "加粗"
  },
  {
    key: "deleteLine",
    name: "删除线"
  }
];
let typeList = [
  {
    key: "text",
    name: "普通文本"
  },
  {
    key: "atId",
    name: "@用户名"
  },
  {
    key: "atAll",
    name: "@所有人"
  },
  {
    key: "link",
    name: "文字链接"
  },
  {
    key: "hr",
    name: "分割线"
  }
];

props.type === "double" && typeList.splice(4, 1);

function typeChange(val) {
  activeForm.value.text = "";
  activeForm.value.url = "";
  activeForm.value.styles = [];
  val === "atAll" && (activeForm.value.text = typeList[2].name);
}
</script>
<template>
  <el-tabs type="card" v-model="state" @tab-change="stateChange">
    <el-tab-pane label="添加" name="add" />
    <el-tab-pane label="编辑" name="edit" disabled />
  </el-tabs>
  <div class="c-set-list">
    <span class="c-set-label"> 填充类型：</span>
    <el-select
      v-model="activeForm.type"
      placeholder="请选择填充类型"
      @change="typeChange"
      :disabled="state === 'edit'"
    >
      <el-option
        v-for="item in typeList"
        :key="item.key"
        :label="item.name"
        :value="item.key"
      />
    </el-select>
  </div>
  <div class="c-set-list" v-show="activeForm.type !== 'hr'">
    <span class="c-set-label"> 文本样式：</span>
    <el-checkbox-group v-model="activeForm.styles">
      <el-checkbox-button
        v-for="item in styleList"
        :label="item.key"
        :key="item.key"
        :disabled="
          activeForm.type.indexOf('at') > -1 && item.key === 'deleteLine'
        "
        >{{ item.name }}
      </el-checkbox-button>
    </el-checkbox-group>
  </div>
  <div class="c-set-list" v-show="activeForm.type !== 'hr'">
    <span class="c-set-label"> 填充内容：</span>
    <el-input
      placeholder="请输入内容"
      v-model="activeForm.text"
      :disabled="activeForm.type === 'atAll'"
    />
  </div>
  <div class="c-set-list" v-show="activeForm.type === 'link'">
    <span class="c-set-label"> 链接地址：</span>
    <el-input
      type="textarea"
      :rows="2"
      placeholder="请输入链接地址"
      v-model="activeForm.url"
      clearable
    />
  </div>
</template>
<style scoped lang="scss">
.c-set-label {
  width: 80px;
  flex: none;
}

.c-set-list {
  margin-bottom: 16px;

  display: flex;
  align-items: center;
}
:deep(.el-tabs__item.is-active) {
  color: #409eff !important;
}
</style>
