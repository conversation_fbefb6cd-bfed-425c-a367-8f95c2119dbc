<script setup lang="ts" name="ButtonSet">
import { ref, computed } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import ButtonSetDialogSetActiveValue from "./ButtonSetDialogSetActiveValue.vue";
interface Props {
  initData: any; //初始化数据
}
interface Emits {
  (e: "update:initData", val: any): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardPreviewItem = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

const colorList = ["default", "primary", "danger"];

let activeValueIndex = ref(-1);
let isModel = ref(false);

function setActiveValue(index) {
  activeValueIndex.value = index;
  isModel.value = true;
}
function deleteActiveValue(index) {
  cardPreviewItem.value.json.actions[0].activeValue.splice(index, 1);
}
function clearActiveValue() {
  cardPreviewItem.value.json.actions[0].activeValue = [];
}
</script>
<template>
  <div>
    <div class="c-set-list">
      <span class="c-set-label"> 按钮样式：</span>
      <el-select
        v-model="cardPreviewItem.json.actions[0].activeColor"
        placeholder="请选择背景颜色"
      >
        <el-option
          v-for="item in colorList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 按钮文本：</span>
      <el-input
        placeholder="请输入按钮文本"
        v-model="cardPreviewItem.json.actions[0].activeText"
        maxlength="10"
        show-word-limit
      />
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 链接地址：</span>
      <el-input
        type="textarea"
        :rows="2"
        placeholder="请输入链接地址"
        v-model="cardPreviewItem.json.actions[0].activeUrl"
        clearable
      />
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 弹框标题：</span>
      <el-input
        placeholder="请输入弹框标题"
        v-model="cardPreviewItem.json.actions[0].activeConfirmTitle"
        clearable
      />
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 弹框内容：</span>
      <el-input
        type="textarea"
        :rows="2"
        placeholder="请输入弹框内容"
        v-model="cardPreviewItem.json.actions[0].activeConfirmText"
        clearable
      />
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 交互数据：</span>
      <el-button plain @click="clearActiveValue">清空</el-button>
      <el-button type="primary" plain @click="setActiveValue(-1)">
        添加
      </el-button>
    </div>
    <div class="c-set-list">
      <span class="c-set-label" />
      <el-table
        :data="cardPreviewItem.json.actions[0].activeValue"
        :border="true"
        highlight-current-row
      >
        <el-table-column property="key" label="key" />
        <el-table-column property="value" label="value" />
        <el-table-column label="操作">
          <template v-slot="scope">
            <el-button
              type="primary"
              :icon="useRenderIcon('edits')"
              circle
              @click="setActiveValue(scope.$index)"
            />
            <el-button
              :icon="useRenderIcon('minus')"
              circle
              @click="deleteActiveValue(scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="isModel">
      <ButtonSetDialogSetActiveValue
        v-model:value="isModel"
        :activeValueIndex="activeValueIndex"
        v-model:initData="cardPreviewItem.json.actions[0].activeValue"
      />
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-set-label {
  width: 80px;
  flex: none;
}

.c-set-list {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
</style>
