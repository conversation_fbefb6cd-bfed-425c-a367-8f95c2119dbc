import { markRaw } from "vue";
import Markdown from "./Markdown.vue";
import Hr from "./Hr.vue";
import Action from "./Action.vue";
import ButtonSet from "./ButtonSet.vue";
import MarkdownSet from "./MarkdownSet.vue";
import HeaderSet from "./HeaderSet.vue";
import Double from "./Double.vue";
import DoubleSet from "./DoubleSet.vue";

export const moduleComponents = [
  {
    key: "header",
    componentName: "Header",
    text: "卡片标题",
    setComponent: markRaw(HeaderSet),
    json: {
      activeColor: "white",
      activeText: "🔔卡片标题🎁"
    }
  },
  {
    key: "markdown",
    componentName: markRaw(Markdown),
    text: "内容",
    setComponent: markRaw(MarkdownSet),
    json: {
      activeList: [
        {
          type: "text",
          text: "内容...",
          url: "",
          styles: []
        }
      ]
    }
  },
  {
    key: "double",
    componentName: markRaw(Double),
    text: "双列文本",
    setComponent: markRaw(DoubleSet),
    json: {
      actions: [
        {
          is_short: true,
          activeList: [
            {
              type: "text",
              text: "🗳来源：",
              url: "",
              styles: []
            },
            {
              type: "text",
              text: "报事报修",
              url: "",
              styles: []
            }
          ]
        },
        {
          is_short: true,
          activeList: [
            {
              type: "text",
              text: "📝类型：",
              url: "",
              styles: []
            },
            {
              type: "text",
              text: "客服工单",
              url: "",
              styles: []
            }
          ]
        }
      ]
    }
  },
  {
    key: "hr",
    componentName: markRaw(Hr),
    text: "分割线",
    setComponent: "",
    json: {
      tag: "hr"
    }
  },
  {
    key: "action_button",
    componentName: markRaw(Action),
    text: "按钮",
    setComponent: markRaw(ButtonSet),
    json: {
      tag: "action",
      actions: [
        {
          tag: "button",
          activeColor: "default",
          activeText: "按钮",
          activeUrl: "",
          activeConfirmTitle: "",
          activeConfirmText: "",
          activeValue: []
        }
      ]
    }
  }
];
