<script setup lang="ts">
import { reactive, computed } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";

let device = useAppStoreHook().device;

interface Props {
  activeValueIndex: number;
  value: boolean;
  initData: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "update:initData", val: any): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});
const activeValue = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const form = reactive(
  props.activeValueIndex > -1
    ? {
        key: props.initData[props.activeValueIndex].key,
        value: props.initData[props.activeValueIndex].value
      }
    : {
        key: "",
        value: ""
      }
);

function submitForm() {
  if (props.activeValueIndex > -1) {
    activeValue.value[props.activeValueIndex] = { ...form };
  } else {
    activeValue.value.push({ ...form });
  }
  handleClose();
}
</script>
<template>
  <el-dialog
    title="添加交互数据key-value"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
    width="50%"
  >
    <el-form
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '140px' : ''"
      ref="formRef"
      :class="{ mobile: device === 'mobile' }"
    >
      <el-row>
        <el-col :lg="4" />
        <el-col :lg="16">
          <el-form-item label="key" prop="key">
            <el-input
              v-model.trim="form.key"
              placeholder="请输入key"
              clearable
            />
          </el-form-item>
          <el-form-item label="value" prop="value">
            <el-input
              clearable
              placeholder="请输入value"
              v-model="form.value"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确定 </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
