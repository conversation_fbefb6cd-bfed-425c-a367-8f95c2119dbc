<script setup lang="ts" name="MarkdownSet">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import CommonContext from "./CommonContext.vue";

interface Props {
  initData: any; //初始化数据
  spanIndex: number;
}
interface Emits {
  (e: "update:initData", val: any): void;
  (e: "update:spanIndex", val: number): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardPreviewItem = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

let state = ref("add");

let initForm = {
  type: "text",
  text: "",
  url: "",
  styles: []
};

let activeForm = reactive({
  ...initForm
});

function setText() {
  if (
    (activeForm.type === "text" ||
      activeForm.type === "link" ||
      activeForm.type === "atId") &&
    !activeForm.text
  ) {
    ElMessage.warning("填充内容不能为空");
    return;
  }
  if (activeForm.type === "link" && !activeForm.url) {
    ElMessage.warning("链接地址不能为空");
    return;
  }
  let form = JSON.parse(JSON.stringify(activeForm));
  form.type === "atId" && (form.text = `@${form.text}`);
  if (state.value === "add") {
    cardPreviewItem.value.json.activeList.push(form);
    stateChange();
  } else {
    cardPreviewItem.value.json.activeList[props.spanIndex] = form;
  }
}
function delText() {
  if (props.initData.json.activeList.length === 1) {
    ElMessage.warning("最少保留一个子内容");
    return;
  }
  cardPreviewItem.value.json.activeList.splice(props.spanIndex, 1);
  state.value = "add";
  for (let key in activeForm) {
    activeForm[key] = initForm[key];
  }
}

function stateChange() {
  if (state.value === "add") {
    emit("update:spanIndex", -1);
    for (let key in activeForm) {
      activeForm[key] = initForm[key];
    }
  }
}
//监听子内容位置spanIndex序号后，进入编辑状态...
watch(
  () => props.spanIndex,
  spanIndex => {
    if (spanIndex < 0) return;
    state.value = "edit";
    let activeList = props.initData.json.activeList;
    let form = JSON.parse(JSON.stringify(activeList[spanIndex]));
    form.type === "atId" && (form.text = form.text.slice(1));
    for (let key in activeForm) {
      activeForm[key] = form[key];
    }
  }
);
</script>
<template>
  <div>
    <CommonContext
      ref="CommonContextRef"
      v-model:activeForm="activeForm"
      v-model:state="state"
      @stateChange="stateChange"
      type="markdown"
    />
    <div class="c-set-list">
      <span class="c-set-label" />
      <el-button type="primary" plain @click="setText">
        {{ state === "edit" ? "保存" : "新增" }}
      </el-button>
      <el-button plain @click="delText" v-show="state === 'edit'">
        删除
      </el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-set-label {
  width: 80px;
  flex: none;
}

.c-set-list {
  margin-bottom: 16px;

  display: flex;
  align-items: center;
}
:deep(.el-tabs__item.is-active) {
  color: #409eff !important;
}
</style>
