<script setup lang="ts" name="Action">
import <PERSON><PERSON> from "./Button.vue";

interface Props {
  initData: any; //初始化数据
}

const props = withDefaults(defineProps<Props>(), {});
</script>
<template>
  <div class="c-action">
    <Button ref="buttonRef" :initData="props.initData" />
  </div>
</template>
<style scoped lang="scss">
.c-action:first-child > div:first-child {
  margin-top: 0;
}
</style>
