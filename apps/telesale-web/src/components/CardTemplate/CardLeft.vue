<script setup lang="ts" name="CardLeft">
import { computed } from "vue";
import { ElMessage } from "element-plus";
import { moduleComponents } from "./moduleComponents";

interface Props {
  cardHeader: any;
  cardPreviewList: any[];
  update_multi: boolean;
}
interface Emits {
  (e: "update:cardHeader", val: any): void;
  (e: "update:cardPreviewList", val: any[]): void;
  (e: "update:update_multi", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardHeader = computed({
  get() {
    return props.cardHeader;
  },
  set(val: any) {
    emit("update:cardHeader", val);
  }
});
const cardPreviewList = computed({
  get() {
    return props.cardPreviewList;
  },
  set(val: any[]) {
    emit("update:cardPreviewList", val);
  }
});
const update_multi = computed({
  get() {
    return props.update_multi;
  },
  set(val: boolean) {
    emit("update:update_multi", val);
  }
});

//在卡片视图中添加新的模块
function addModule(item) {
  if (item.key === "header") {
    if (cardHeader.value) {
      ElMessage.warning("卡片标题只能有一个");
    } else {
      let header = JSON.parse(JSON.stringify(item));
      header.setComponent = item.setComponent;
      cardHeader.value = header;
      if (!cardPreviewList.value.length) {
        let newObj = JSON.parse(JSON.stringify(moduleComponents[1]));
        newObj.ref = newObj.key + "0" + new Date().getTime();
        newObj.componentName = moduleComponents[1].componentName;
        newObj.setComponent = moduleComponents[1].setComponent;
        cardPreviewList.value.push(newObj);
      }
    }
  } else {
    let newObj = JSON.parse(JSON.stringify(item));
    newObj.ref =
      newObj.key + cardPreviewList.value.length + new Date().getTime();
    newObj.componentName = item.componentName;
    newObj.setComponent = item.setComponent;
    cardPreviewList.value.push(newObj);
  }
}
</script>
<template>
  <div class="c-index-left">
    <div class="c-head c-border">
      <p class="c-head-title">卡片属性配置</p>
      <div class="c-item">
        卡片是否共享：
        <el-switch v-model="update_multi" />
      </div>
    </div>
    <div class="c-head">
      <p class="c-head-title">模块组件</p>
      <div class="c-head-text">点击模块组件，即可将其添加至卡片底部</div>
    </div>
    <div class="c-box">
      <div
        class="c-list"
        v-for="item in moduleComponents"
        :key="item.key"
        @click="addModule(item)"
      >
        <span>{{ item.text }}</span>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-index-left {
  width: 300px;
  background-color: #fff;
  user-select: none;

  .c-border {
    border-bottom: 1px dashed #ccc;
    .c-item {
      margin-top: 10px;
    }
  }

  .c-head {
    padding: 16px;
    background-color: #fff;
    position: sticky;

    .c-head-title {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .c-head-text {
      color: #646a73;
      margin-top: 4px;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .c-box {
    padding: 0 16px;
    height: calc(100% - 80px);
    overflow-y: auto;

    .c-list {
      margin-bottom: 12px;
      cursor: pointer;
      border: 1px solid #dee0e3;
      border-radius: 5px;
      height: 40px;
      line-height: 40px;
      text-align: center;

      &:hover {
        box-shadow: 0 4px 8px rgba(31, 35, 41, 0.1);
      }
    }
  }
}
</style>
