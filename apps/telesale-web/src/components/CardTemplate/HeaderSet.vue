<script setup lang="ts" name="HeaderSet">
import { reactive, computed } from "vue";

interface Props {
  initData: any; //初始化数据
}
interface Emits {
  (e: "update:initData", val: any): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardHeader = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

let colorList = [
  "white",
  "blue",
  "wathet",
  "turquoise",
  "green",
  "yellow",
  "orange",
  "red",
  "carmine",
  "violet",
  "purple",
  "indigo",
  "grey"
];

const form = reactive({
  activeColor: props.initData.json.activeColor || "",
  activeText: props.initData.json.activeText || ""
});

function setColor(val) {
  cardHeader.value.json.activeColor = val;
}

function setText(val) {
  cardHeader.value.json.activeText = val;
}
</script>
<template>
  <div>
    <div class="c-set-list">
      <span class="c-set-label"> 背景颜色：</span>
      <el-select
        v-model="form.activeColor"
        placeholder="请选择背景颜色"
        @change="setColor"
      >
        <el-option
          v-for="item in colorList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 标题内容：</span>
      <el-input
        type="textarea"
        :rows="3"
        placeholder="请输入内容"
        v-model="form.activeText"
        maxlength="50"
        show-word-limit
        @input="setText"
      />
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-set-label {
  width: 80px;
  flex: none;
}
.c-set-list {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
</style>
