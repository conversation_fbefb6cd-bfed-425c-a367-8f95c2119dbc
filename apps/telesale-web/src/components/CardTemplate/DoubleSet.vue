<script setup lang="ts" name="DoubleSet">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import CommonContext from "./CommonContext.vue";

interface Props {
  initData: any; //初始化数据
  divIndex: number;
  spanIndex: number;
}
interface Emits {
  (e: "update:initData", val: any): void;
  (e: "update:divIndex", val: number): void;
  (e: "update:spanIndex", val: number): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardPreviewItem = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

let state = ref("add");

let initForm = {
  type: "text",
  text: "",
  url: "",
  styles: [],
  is_short: true
};

let activeForm = reactive({
  ...initForm
});

function testMath() {
  if (
    (activeForm.type === "text" ||
      activeForm.type === "link" ||
      activeForm.type === "atId") &&
    !activeForm.text
  ) {
    ElMessage.warning("填充内容不能为空");
    return false;
  }
  if (activeForm.type === "link" && !activeForm.url) {
    ElMessage.warning("链接地址不能为空");
    return false;
  }
  return true;
}

function setText() {
  if (!testMath()) return;
  let form = JSON.parse(JSON.stringify(activeForm));
  form.type === "atId" && (form.text = `@${form.text}`);
  cardPreviewItem.value.json.actions[props.divIndex].is_short = form.is_short;
  if (state.value === "add") {
    cardPreviewItem.value.json.actions[props.divIndex].activeList.push(form);
    stateChange();
  } else {
    cardPreviewItem.value.json.actions[props.divIndex].activeList[
      props.spanIndex
    ] = form;
  }
}
function delText() {
  if (props.initData.json.actions[props.divIndex].activeList.length === 1) {
    ElMessage.warning("最少保留一个子内容");
    return;
  }
  cardPreviewItem.value.json.actions[props.divIndex].activeList.splice(
    props.spanIndex,
    1
  );
  state.value = "add";
}

function addColumn() {
  if (!testMath()) return;
  let form = JSON.parse(JSON.stringify(activeForm));
  form.type === "atId" && (form.text = `@${form.text}`);
  cardPreviewItem.value.json.actions.push({
    is_short: form.is_short,
    activeList: [form]
  });
}

function delColumn() {
  if (props.initData.json.actions.length <= 2) {
    ElMessage.warning("最少保留两列");
    return;
  }
  cardPreviewItem.value.json.actions.splice(props.divIndex, 1);
  state.value = "add";
  emit("update:divIndex", -1);
}

function stateChange() {
  if (state.value === "add") {
    emit("update:spanIndex", -1);
    for (let key in activeForm) {
      activeForm[key] = initForm[key];
    }
    activeForm.is_short =
      cardPreviewItem.value.json.actions[props.divIndex].is_short;
  }
}
//监听子内容位置spanIndex序号后，进入编辑状态...
watch(
  [() => props.divIndex, () => props.spanIndex],
  ([divIndex, spanIndex]) => {
    if (spanIndex < 0 || divIndex < 0) return;
    state.value = "edit";
    let activeList = props.initData.json.actions[props.divIndex].activeList;
    let form = JSON.parse(JSON.stringify(activeList[spanIndex]));
    form.type === "atId" && (form.text = form.text.slice(1));
    for (let key in activeForm) {
      activeForm[key] = form[key];
    }
    activeForm.is_short = props.initData.json.actions[props.divIndex].is_short;
  }
);
</script>
<template>
  <div>
    <CommonContext
      ref="CommonContextRef"
      v-model:activeForm="activeForm"
      v-model:state="state"
      @stateChange="stateChange"
      type="double"
    />
    <div class="c-set-list">
      <span class="c-set-label"> 所在列宽：</span>
      <el-select v-model="activeForm.is_short">
        <el-option label="50%" :value="true" />
        <el-option label="100%" :value="false" />
      </el-select>
    </div>
    <div class="c-set-list">
      <span class="c-set-label" />
      <el-button
        type="primary"
        plain
        @click="setText"
        :disabled="props.divIndex < 0"
      >
        {{ state === "edit" ? "保存" : "新增" }}
      </el-button>
      <el-button
        type="success"
        plain
        @click="addColumn"
        v-show="state === 'add'"
      >
        新增列
      </el-button>
      <el-button plain @click="delText" v-show="state === 'edit'">
        删除
      </el-button>
      <el-button
        type="danger"
        plain
        @click="delColumn"
        v-show="state === 'edit'"
      >
        删除所在列
      </el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-set-label {
  width: 80px;
  flex: none;
}

.c-set-list {
  margin-bottom: 16px;

  display: flex;
  align-items: center;
}
</style>
