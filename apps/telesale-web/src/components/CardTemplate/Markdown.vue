<script setup lang="ts" name="Markdown">
import { computed } from "vue";

interface Props {
  initData: any; //初始化数据
}
interface Emits {
  (e: "update:initData", val: any): void;
  (e: "update:spanIndex", val: number): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardPreviewItem = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

function classHandler(item) {
  return {
    bold: item.styles.indexOf("bold") > -1,
    italics: item.styles.indexOf("italics") > -1,
    deleteLine: item.styles.indexOf("deleteLine") > -1,
    blueColor: item.type === "link" || item.type.indexOf("at") > -1,
    hr: item.type === "hr"
  };
}
function markdownClick(e) {
  if (e.target.dataset.id === "span") {
    emit("update:spanIndex", Number(e.target.dataset.index));
  }
}
</script>
<template>
  <div class="c-markdown" @dblclick="markdownClick($event)">
    <span
      v-for="(item, index) in cardPreviewItem.json.activeList"
      :key="index"
      :class="classHandler(item)"
      data-id="span"
      :data-index="index"
    >
      <div v-if="item.styles.indexOf('changeLine') > -1" />
      {{ item.text }}
    </span>
  </div>
</template>
<style scoped lang="scss">
.c-markdown {
  > span {
    line-height: 1.6;
    cursor: pointer;

    &:last-child.hr {
      margin-bottom: 0;
    }
  }

  .bold {
    font-weight: bold;
  }

  .italics {
    font-style: italic;
  }

  .deleteLine {
    text-decoration: line-through;
  }

  .blueColor {
    color: #245bdb;
  }

  .hr {
    display: block;
    margin-bottom: 16px;
    height: 17px;
    background-color: #fff;
    border-bottom: 1px solid #1f232926;
  }
}
</style>
