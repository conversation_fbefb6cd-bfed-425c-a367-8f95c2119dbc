<!--
 * @Date         : 2024-10-09 13:47:49
 * @Description  : 搜索弹窗
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useCollectStore } from "/@/store/modules/collect";
import {
  addClueCollectApi,
  CollectList,
  delClueCollectApi
} from "/@/api/customer/collect";
import { Folder, InfoFilled, Star, StarFilled } from "@element-plus/icons-vue";
import { PopoverInstance } from "element-plus";

const props = defineProps<{
  successHide?: boolean;
  collectInfo?: {
    id: number;
    infoUuid: string;
  };
}>();
const emist = defineEmits(["success", "remove"]);

const useCollect = useCollectStore();
const { collectList } = storeToRefs(useCollect);
const loading = ref(false);
const popoverRef = ref<PopoverInstance>();
const hasCollect = computed(() => {
  return collectList.value.find(item => item.id === props.collectInfo?.id);
});
const cancel = () => {
  removeCollect(props.collectInfo?.id, true);
};

const collectHandle = (item: CollectList) => {
  if (getCurrentCollect(item.id)) {
    removeCollect(item.id);
  } else {
    addCollect(item.id);
  }
};

const getCurrentCollect = (id: number) => {
  return props.collectInfo?.id && props.collectInfo?.id === id;
};

const mouseoverFn = (item: CollectList) => {
  item.showHandle = true;
};

const mouseleaveFn = (item: CollectList) => {
  item.showHandle = false;
};

const addCollect = (id: number) => {
  loading.value = true;
  addClueCollectApi({
    infoUuid: props.collectInfo?.infoUuid,
    id: id
  })
    .then(() => {
      emist("success", id);
      if (props.successHide) {
        popoverRef.value?.hide();
      }
      ElMessage.success("已添加至收藏");
    })
    .finally(() => {
      loading.value = false;
    });
};

const removeCollect = (id: number, isClose: boolean = false) => {
  loading.value = true;
  delClueCollectApi({
    infoUuid: [props.collectInfo?.infoUuid]
  })
    .then(() => {
      emist("remove", id);
      if (isClose || props.successHide) {
        popoverRef.value?.hide();
      }
      ElMessage.success("已取消收藏");
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(async () => {
  if (!collectList.value.length) {
    await useCollect.getCollectList();
  }
});
</script>

<template>
  <el-popover placement="bottom" ref="popoverRef" :width="280" trigger="click">
    <template #reference>
      <slot />
    </template>
    <div v-loading="loading">
      <div class="font-bold flex items-center gap-5px">
        添加到收藏
        <el-popover placement="left" :width="900" trigger="hover">
          <template #reference>
            <el-icon><InfoFilled /></el-icon>
          </template>
          <img
            class="w-full"
            src="https://fp.yangcong345.com/middle/1.0.0/telesale-web-v2/20241015-160552.gif"
          />
        </el-popover>
      </div>
      <div class="text-12px">
        <span>点击添加到收藏页指定文件夹</span>
        <template v-if="hasCollect">
          <span>，或</span>
          <span
            @click="cancel"
            class="cursor-pointer c-[var(--el-color-primary)]"
          >
            取消收藏
          </span>
        </template>
      </div>
      <div class="max-h-300px overflow-y-auto">
        <div
          v-for="(item, index) in collectList"
          :key="index"
          class="flex justify-between items-center p-5px hover:bg-[#afe4fd] cursor-pointer mt-5px h-34px"
          :class="{
            'bg-[#afe4fd]': getCurrentCollect(item.id)
          }"
          @mouseover="mouseoverFn(item)"
          @mouseleave="mouseleaveFn(item)"
          @click="collectHandle(item)"
        >
          <div class="flex items-center">
            <el-icon :size="16">
              <Folder />
            </el-icon>
            <span class="ml-4px">
              {{ item.bookmarkName }}
            </span>
          </div>
          <div v-show="item.showHandle || getCurrentCollect(item.id)">
            <el-icon
              :size="getCurrentCollect(item.id) ? 22 : 20"
              color="var(--el-color-primary)"
            >
              <StarFilled v-if="getCurrentCollect(item.id)" />
              <Star v-else />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<style lang="scss" scoped></style>
