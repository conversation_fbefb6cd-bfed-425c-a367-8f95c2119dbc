<!--
 * @Date         : 2024-03-27 16:42:09
 * @Description  : 上传组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script setup lang="ts" name="PosterTheme">
import { computed, ref, watch } from "vue";
import { ElMessage, UploadInstance, UploadRequestOptions } from "element-plus";
import { upload, getPic } from "/@/api/order";

interface Props {
  path: string[];
  max?: number;
  size?: number;
}
interface Emits {
  (e: "update:path", val: string[]): void;
  (e: "update:loading", val: boolean): void;
}
const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  size: 1 // 单位：M
});

const fileList = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const uploadList = ref([]);
const dialogImageUrl = ref();
const uploadRefs = ref<UploadInstance>();

const handleRemove = file => {
  uploadList.value = uploadList.value.filter(item => item.uid !== file.uid);
};

const handlePictureCardPreview = uploadFile => {
  dialogImageUrl.value = uploadFile.url!;
  dialogVisible.value = true;
};

const handleExceed = () => {
  ElMessage.warning("最多只能上传" + props.max + "张图片");
};

const handlePaste = (event: ClipboardEvent) => {
  if (loading.value) return;
  const items = event.clipboardData.items;
  let file = null;
  if (!items || items.length === 0) {
    ElMessage.error("当前浏览器不支持本地");
    return;
  }
  // 搜索剪贴板items
  for (let i = 0; i < items.length; i++) {
    if (items[i].type.indexOf("image") !== -1) {
      file = items[i].getAsFile();
      break;
    }
  }

  if (!file) {
    ElMessage.error("粘贴内容非图片");
    return;
  }
  if (fileList.value.length >= props.max) {
    handleExceed(); // 图片数量超出
    return;
  }

  const fileCopy = { file };
  uploadPic(fileCopy as UploadRequestOptions).then(() => {
    uploadRefs.value.handleStart(file);
    uploadList.value[uploadList.value.length - 1].uid = fileCopy.file.uid;
  });
};

function uploadPic(file: UploadRequestOptions) {
  if (file.file.size / 1024 / 1024 > props.size) {
    ElMessage.warning(`图片大小不能超过${props.size}M`);
    return Promise.reject();
  }
  let formData = new FormData();
  formData.append("file", file.file);
  loading.value = true;
  return upload(formData)
    .then(({ data }) => {
      uploadList.value.push({
        uid: file.file.uid,
        fileNmae: data
      });
    })
    .catch(() => {
      return Promise.reject();
    })
    .finally(() => {
      loading.value = false;
    });
}

const init = async () => {
  fileList.value = [];
  uploadList.value = [];
  if (props.path?.length) {
    for (const key of props.path) {
      const res = await getPic(key);
      fileList.value.push({
        name: key,
        url: res.data
      });
    }
  }
  nextTick(() => {
    uploadList.value = fileList.value.map(item => {
      return {
        uid: item.uid,
        fileNmae: item.name
      };
    });
    console.log(fileList.value, uploadList.value);
  });
};

watch(
  () => uploadList.value,
  () => {
    const value = uploadList.value.map(item => item.fileNmae);
    emit("update:path", value);
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  init
});
</script>
<template>
  <div class="d-box" v-loading="loading">
    <el-upload
      ref="uploadRefs"
      v-model:fileList="fileList"
      action="#"
      :http-request="uploadPic"
      accept=".jpg,.jpeg,.png,.JPG,.JPEG"
      list-type="picture-card"
      :limit="props.max"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleExceed"
      v-bind="$attrs"
      :class="{
        'hide-upload': props.max && fileList.length >= props.max
      }"
    >
      <div class="d-text" @paste="handlePaste">
        <el-icon>
          <IconifyIconOffline icon="plus" />
        </el-icon>
        <el-link type="primary" class="tip" @click.stop>
          点击此处粘贴图片即可上传
        </el-link>
      </div>
    </el-upload>
    <el-dialog title="预览" v-model="dialogVisible">
      <img class="w-full mb-20px" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.d-pic {
  width: 148px;
  height: 148px;
  display: block;
}

.d-upload {
  width: 150px;
  height: 150px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  text-align: center;

  :deep(.el-upload:focus) {
    color: #2c3e50;
  }
}

.d-text {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  width: 150px;
  height: 150px;
  cursor: pointer;
}
.tip {
  width: 100%;
  font-size: 12px;
}

:deep(.hide-upload) {
  .el-upload--picture-card {
    display: none !important;
  }
}
</style>
