import { http } from "../utils/http";
import baseURL from "./url";

//出勤列表
export const getActual = params => {
  return http.request("get", `${baseURL.api}/web/actual_schedule/list`, {
    params
  });
};

//出勤列表编辑
export const setActual = data => {
  return http.request("post", `${baseURL.api}/web/actual_schedule`, { data });
};

//查看目标
export const getSelfTarget = params => {
  return http.request("get", `${baseURL.api}/web/worker_goal`, { params });
};

//根据组织架构ID获取组织架构目标
export const getIdTarget = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-salary/group_goal/takeByOrgID`,
    {
      params
    }
  );
};

//个人目标
export const getPersonTarget = params => {
  return http.request("get", `${baseURL.api}/web/worker_goal/list`, {
    params
  });
};
//小组目标
export const getGroupTarget = params => {
  return http.request("get", `${baseURL.api}/web/group_goal/list`, {
    params
  });
};
//团队目标
export const getTeamTarget = params => {
  return http.request("get", `${baseURL.api}/web/goal/list`, {
    params
  });
};

//更新目标
export const updateTarget = data => {
  return http.request("post", `${baseURL.api}/web/update_goal`, {
    data
  });
};

//目标导出
export const targetExport = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-salary/goal/export`,
    {
      params
    },
    {
      responseType: "blob"
    }
  );
};

//刷新坐席
export const refreshTarget = params => {
  return http.request("get", `${baseURL.api}/web/goal/refresh_worker`, {
    params
  });
};

//查看薪资
export const getSalaryList = params => {
  return http.request("get", `${baseURL.api}/web/salary/list`, {
    params
  });
};

//导出薪资
export const exportSalaryList = params => {
  return http.request("get", `${baseURL.api}/web/salary/export`, {
    params
  });
};
