import { CueModelForm, CueModelInfo } from "../types/system/cueModel";
import { http } from "../utils/http";
import baseURL from "./url";

// 获取全局分配设置
export const getDistribute = params => {
  return http.request("get", `${baseURL.api}/web/admin/setting`, {
    params
  });
};

// 保存全局分配设置
export const setDistribute = data => {
  return http.request("post", `${baseURL.api}/web/admin/setting/save`, {
    data
  });
};

// 获取团/组线索分配
export const getGroupDistributeApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/group/allocate/list`,
    {
      params
    }
  );
};

// 保存团/组线索分配
export const setGroupDistributeApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/group/allocate`,
    {
      data
    }
  );
};

// 获取私域设置列表
export const getMvpList = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/mvp/settings/list`
  );
};

// 获取私域设置详情
export const getMvpDetail = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/mvp/settings`,
    {
      params
    }
  );
};

// 删除私域设置
export const delMvp = params => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-datapool/admin/mvp/settings`,
    {
      params
    }
  );
};

// 创建私域设置
export const addMvp = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/mvp/settings`,
    {
      data
    }
  );
};

// 编辑私域设置
export const editMvp = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-datapool/admin/mvp/settings`,
    {
      data
    }
  );
};

// 获取线索定向分配
export const getOrientationListApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/leads/import/list`,
    {
      params
    }
  );
};

// 新增线索定向分配
export const addOrientationApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/leads/import`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

// 读取线索权重设置
export const getRule = params => {
  return http.request("get", `${baseURL.api}/web/info/allocate/rule`, {
    params
  });
};

// 设置线索权重分配规则
export const setRule = data => {
  return http.request("post", `${baseURL.api}/web/info/allocate/rule`, {
    data
  });
};

// 获取线索标签设置
export const getTag = (params?: { accountId: number }) => {
  return http.request(
    "get",
    `${baseURL.api}/web/admin/setting/wecom/tag/list`,
    { params }
  );
};

// 保存线索标签设置
export const setTag = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/admin/setting/wecom/tag/save`,
    {
      data
    }
  );
};

// 获取设定接收客服线索的坐席
export const getPushAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/admin/setting/kf/info/worker/list`,
    {
      data
    }
  );
};

// 设定接收客服线索的坐席
export const setPushAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/admin/setting/kf/info/worker/save`,
    {
      data
    }
  );
};

// 获取接收待支付平板的坐席
export const getPadAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/admin/setting/pad/worker/list`,
    {
      data
    }
  );
};

// 设定接收待支付平板的坐席
export const setPadAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/admin/setting/pad/worker/save`,
    {
      data
    }
  );
};

//获取新媒体小程序线索接收人
export const getLiveAgent = params => {
  return http.request("get", `${baseURL.api}/wuhan-miniprogram/media/worker`, {
    params
  });
};

// 设置新媒体小程序线索接收人
export const setLiveAgent = data => {
  return http.request("post", `${baseURL.api}/wuhan-miniprogram/media/worker`, {
    data
  });
};

//获取研学坐席
export const getResearchAgent = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/research/worker`,
    {
      params
    }
  );
};

//保存研学坐席
export const setResearchAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/research/worker`,
    {
      data
    }
  );
};

// 飞书推送开关
export const pushSwitch = params => {
  return http.request("get", `${baseURL.api}/web/admin/setting/cron/switch`, {
    params
  });
};

//获取AB测设置
export const getAB = params => {
  return http.request("get", `${baseURL.api}/web/admin/absetting`, {
    params
  });
};

// 保存AB测设置
export const setAB = data => {
  return http.request("post", `${baseURL.api}/web/admin/absetting/save`, {
    data
  });
};

//省市区
export const getAddress = () => {
  return http.request("get", `${baseURL.api}/web/region/all`);
};

//学校
export const getSchool = params => {
  return http.request("get", `${baseURL.api}/web/region/school/${params.code}`);
};

//查询屏蔽规则
export const getShieldRule = params => {
  return http.request("get", `${baseURL.api}/web/blockrule/load/${params}`);
};

// 保存AB测设置
export const setShieldRule = data => {
  return http.request("post", `${baseURL.api}/web/blockrule/save`, {
    data
  });
};

//查询提成设置
export const getSalaryCoefficient = params => {
  return http.request("get", `${baseURL.api}/web/rate_setting`, {
    params
  });
};

// 设置提成设置
export const setSalaryCoefficient = data => {
  return http.request("post", `${baseURL.api}/web/rate_setting`, {
    data
  });
};

//获取岗位薪资
export const getPostSalary = params => {
  return http.request("get", `${baseURL.api}/web/salary_setting`, {
    params
  });
};

//设置岗位薪资
export const setPostSalary = data => {
  return http.request("post", `${baseURL.api}/web/salary_setting`, {
    data
  });
};

//查询奖金设置
export const getBonus = params => {
  return http.request("get", `${baseURL.api}/web/bonus_setting`, {
    params
  });
};

//奖金设置
export const setBonus = data => {
  return http.request("post", `${baseURL.api}/web/bonus_setting`, {
    data
  });
};

// 获取设定接收客服线索的坐席
export const getQualityAgent = () => {
  return http.request("get", `${baseURL.api}/web/qa/setting/quality_worker`);
};

// 设定接收客服线索的坐席
export const setQualityAgent = data => {
  return http.request("post", `${baseURL.api}/web/qa/setting/quality_worker`, {
    data
  });
};

// 获取礼品卡活动时间
export const getGiftTime = () => {
  return http.request("get", `${baseURL.api}/web/gift/activeTime`);
};

// 设置礼品卡活动时间
export const setGiftTime = data => {
  return http.request("post", `${baseURL.api}/web/gift/setActiveTime`, {
    data
  });
};

// 获取葱葱有礼活动时间
export const getOnionTime = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/activities/take/lotteryDraw`
  );
};

// 设置葱葱有礼活动时间
export const setOnionTime = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/activities/lotteryDraw`,
    {
      data
    }
  );
};

export interface ActiveTimeRes {
  id: number;
  name: string;
  begin: string;
  end: string;
  extra: string;
  state: number;
  createdAt: string;
  updatedAt: string;
  alias: string;
  time: number[];
}

// 获取活动时间
export const getActiveTimeApi = alias => {
  return http.request<ActiveTimeRes>(
    "get",
    `${baseURL.api}/wuhan-miniprogram/activities/take/${alias}`
  );
};

// 设置活动时间
export const setActiveTimeApi = (data: ActiveTimeRes) => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/activities/${data.alias}`,
    {
      data
    }
  );
};

// 获取质检时长设置
export const getCheckDuration = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-qa/qa/setting/quality_duration`
  );
};

// 保存质检时长设置
export const setCheckDuration = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-qa/qa/setting/quality_duration`,
    {
      data
    }
  );
};

//报单群列表
export const getListGroupID = params => {
  return http.request("get", `${baseURL.api}/sync-order/settings/chat/list`, {
    params
  });
};

//添加报单群设置
export const addGroupID = data => {
  return http.request("post", `${baseURL.api}/sync-order/settings/chat`, {
    data
  });
};

//编辑报单群设置
export const editGroupID = data => {
  return http.request("put", `${baseURL.api}/sync-order/settings/chat`, {
    data
  });
};

//删除报单群设置
export const delGroupID = params => {
  return http.request(
    "delete",
    `${baseURL.api}/sync-order/settings/chat/${params.id}`
  );
};

// 获取短信列表
export const getSmsTemplateApi = (params: { siteId: number }) => {
  return http.request<{
    list: {
      id: number;
      name: string;
      content: string;
      templateld: string;
    }[];
  }>("get", `${baseURL.api}/wuhan-worker/marketingSMS`, { params });
};

// 获取短信详情
export const getSmsInfoApi = (id: number) => {
  return http.request("get", `${baseURL.api}/wuhan-worker/marketingSMS/${id}`);
};

// 删除单条营销短信
export const deleteSmsInfoApi = (id: number) => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-worker/marketingSMS/${id}`
  );
};

// 创建短信
export const addSmsTemplateApi = (data: {
  templateIds: string[];
  siteId: number;
}) => {
  return http.request<{
    successful: number;
    failed: number;
    errors: string[];
  }>("post", `${baseURL.api}/wuhan-worker/marketingSMS`, {
    data
  });
};
