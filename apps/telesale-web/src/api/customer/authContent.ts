/*
 * @Date         : 2024-08-29 18:54:31
 * @Description  : 内容权限
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface AuthInfo {
  data: {
    _id: string;
    oldId: string;
    userId: string;
    sn: string;
    sourceId: SourceId;
    sourceType: string;
    targetType: string;
    targetId: string;
    duration: number;
    state: string;
    remarks: Remark[];
    createdAt: string;
    updatedAt: string;
    stage?: number;
    startTime?: string;
    endTime?: string;
  }[];
}

interface Remark {
  tag: string;
  content: string;
  createdBy: SourceId;
  creatorType: string;
}

interface SourceId {
  name: string;
  id: string;
  objectId: string;
}

/**
 * @description: 获取内容权限列表
 * @param {string} userId
 */
export const getAuthListApi = (params: { userId: string }) => {
  return http.request<AuthEndInfo>(
    "get",
    `${baseURL.api}/wuhan-datapool/info/getUserAuthByUserId`,
    {
      params
    }
  );
};

export interface AuthEndInfo {
  data: {
    collectionCourses: SpecialCourse[];
    correction: SpecialCourse[];
    examPaper: SpecialCourse[];
    m2: SpecialCourse[];
    problemAnalysisTrial: SpecialCourse[];
    problemTrial: SpecialCourse[];
    publicTextbook: SpecialCourse[];
    publicTextbookTrial: SpecialCourse[];
    specialCourse: SpecialCourse[];
    specialCourseTrial: SpecialCourse[];
    tenant: SpecialCourse[];
    topic: SpecialCourse[];
    topicTrial: SpecialCourse[];
    vip: SpecialCourse[];
    zhiYuan: SpecialCourse[];
  };
}

export interface SpecialCourse {
  id: string;
  expired: boolean;
  expireTime: string;
  sourceType?: string;
  stage?: number;
  details: AuthInfo[];
}

/**
 * @description: 获取内容权限总截止时间
 * @param {string} userId
 */
export const getAuthEndApi = (params: { userId: string }) => {
  return http.request<{
    data: AuthEndInfo[];
  }>("get", `${baseURL.api}/web/customer/auth/info/endInfo`, { params });
};

/**
 * @description: 获取学科/学段/教材/年级筛选参数
 * @param {string} userId
 */
export const getSubjectsListApi = (params: { userId: string }) => {
  return http.request<any[]>(
    "get",
    `${baseURL.api}/web/customer/auth/info/subjects`,
    { params }
  );
};

/**
 * @description: 获取电子试卷
 * @param {string} userId
 */
export const getExamPaperPkgApi = (data: { userId: string }) => {
  return http.request<{
    list: any[];
  }>("post", `${baseURL.api}/web/customer/auth/info/searchExamPaperPkg`, {
    data: {
      limit: 10000
    }
  });
};
