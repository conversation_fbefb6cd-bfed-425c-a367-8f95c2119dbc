/*
 * @Date         : 2024-09-11 17:28:44
 * @Description  : 外呼相关的api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * @description: 通知App渠道变更
 * @param {*}
 * @return {*}
 */
export const noticeChangeChannelApi = () => {
  return http.request(
    "get",
    `http://127.0.0.1:9876/channel/reload`,
    {},
    {
      hideError: true
    }
  );
};

/**
 * @description: 切换外呼渠道
 * @param {number} channelId
 * @return {*}
 */
export const changeCallChannelApi = (data: { channelId: number }) => {
  return http.request("post", `${baseURL.api}/wuhan-callphone/change/channel`, {
    data
  });
};
