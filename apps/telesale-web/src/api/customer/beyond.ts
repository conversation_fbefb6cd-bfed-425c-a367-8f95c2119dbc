/*
 * @Date         : 2024-03-27 11:56:46
 * @Description  : 线索查询下的apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";
import { PhoneInfoReq, PhoneInfoRes } from "/@/types/customer/beyond";

/**
 * @description: 查询手机号和洋葱Id对应的坐席归属
 * @param {PhoneInfoReq} data
 */
export const getPhoneInfoApi = (data: PhoneInfoReq) => {
  return http.request<PhoneInfoRes>(
    "post",
    `${baseURL.api}/web/customer/phone/info`,
    {
      data
    }
  );
};

export interface OrderInfo {
  orderId: string;
  userId: string;
  goodName: string;
}

/**
 * @description: 可绑定订单
 * @param {string} phone
 */
export const getBindOrderApi = (params: Pick<PhoneInfoReq, "phone">) => {
  return http.request<OrderInfo>(
    "get",
    `${baseURL.api}/wuhan-miniprogram/externalUser/order/listBindOrder`,
    {
      params
    }
  );
};

export interface BindOrder {
  userId: string;
  orderId: string;
}

/**
 * @description: 绑定订单
 * @param {BindOrder} data
 */
export const bindOrderApi = (data: BindOrder) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/externalUser/order/bind`,
    {
      data
    }
  );
};

/**
 * @description: 生成转介绍小程序链接
 */
export const createWechatLinkApi = () => {
  return http.request<{
    link: string;
  }>("get", `${baseURL.api}/wuhan-marketing/referral/GetReferralLink`);
};
