/*
 * @Date         : 2024-12-20 16:54:56
 * @Description  : 家庭号相关的api
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * @description: 更新家庭成员手机号类型
 */
export const updateFamilyPhoneTypeApi = (data: {
  familyMemberId: string;
  phoneType: string;
}) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/family/updateFamilyMemberPhoneType`,
    {
      data
    }
  );
};

/**
 * @description: 更新学情信息绑定类型
 */
export const updateStudyInfoApi = (data: {
  userId: string;
  studyInfoBindType: string;
}) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/family/updateStudyInfoBindType`,
    {
      data
    }
  );
};

export interface FamilyInfo {
  id?: string;
  category?: string;
  state?: string;
  uneffectTime?: string;
  familyMembers?: FamilyMembersInfo[];
  familyClues?: FamilyMembersInfo[];
  isHolding?: boolean;
}
export interface FamilyMembersInfo {
  id?: string;
  familyId?: string;
  type?: "clue" | "user";
  userId?: string;
  phone?: string;
  phoneType?: string;
  studyInfoBindedType?: string;
  relatedUserId?: string;
  workerId?: number;
  userExpire?: string;
  isDeal?: boolean;
  contactState?: string;
  holdStatus?: string;
  holdVerifyStatus?: string;
  infoUuid: string;
  onionId?: string;
}

/**
 * @description: 获取销售可见的家庭详情
 */
export const getFamilyDetailApi = (params: { familyId: string }) => {
  return http.request<FamilyInfo>(
    "get",
    `${baseURL.api}/wuhan-datapool/family/getSalesFamilyDetail`,
    {
      params
    }
  );
};

export interface PostWuhanDatapoolFamilyAddFamilyClueReqBodyOther {
  relatedUserId?: string;
  phone?: string;
  phoneType?: string;
  channel?: string;
  [k: string]: unknown;
}

/**
 * @description 添加手机号线索
 * https://yapi.yc345.tv/project/2352/interface/api/118148
 * <AUTHOR>
 * @date 2024-12-27
 * @export
 * @param {PostWuhanDatapoolFamilyAddFamilyClueReqBodyOther} data
 * @returns {Promise<PostWuhanDatapoolFamilyAddFamilyClueResBody>}
 */
export const addPhoneClueApi = (
  data: PostWuhanDatapoolFamilyAddFamilyClueReqBodyOther
) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/addFamilyClue`,
    {
      data
    }
  );
};

export interface PostWuhanDatapoolFamilyDeleteFamilyClueReqBodyOther {
  id?: string;
  phone: string;
}

/**
 * @description 删除手机号线索
 * https://yapi.yc345.tv/project/2352/interface/api/118169
 * <AUTHOR>
 * @date 2024-12-27
 * @export
 * @param {PostWuhanDatapoolFamilyDeleteFamilyClueReqBodyOther} data
 * @returns {Promise<PostWuhanDatapoolFamilyDeleteFamilyClueResBody>}
 */
export const delPhoneClueApi = (
  data: PostWuhanDatapoolFamilyDeleteFamilyClueReqBodyOther
) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/deleteFamilyClue`,
    {
      data
    }
  );
};

export interface GetWuhanDatapoolFamilyGetFamilyByUserIDReqQuery {
  /**
   * 用户id
   */
  userID?: string;
}

/**
 * @description 根绝用户id查询用户所属家庭详细信息
 * https://yapi.yc345.tv/project/2352/interface/api/118294
 * <AUTHOR>
 * @date 2024-12-31
 * @export
 * @param {GetWuhanDatapoolFamilyGetFamilyByUserIDReqQuery} params
 * @returns {Promise<GetWuhanDatapoolFamilyGetFamilyByUserIDResBody>}
 */
export const getFamilyInfoApi = (
  params: GetWuhanDatapoolFamilyGetFamilyByUserIDReqQuery
) => {
  return http.request<FamilyInfo>(
    `get`,
    `${baseURL.api}/wuhan-datapool/family/getFamilyByUserID`,
    {
      params
    }
  );
};

export interface GetWuhanDatapoolFamilyGetFamilyOrderInfoReqQuery {
  /**
   * 家庭ID
   */
  familyId?: string;
}

export interface GetWuhanDatapoolFamilyGetFamilyOrderInfoResBody {
  familyId?: string;
  familyOrderInfos?: FamilyOrderInfo[];
}
export interface FamilyOrderInfo {
  userId?: string;
  onionId?: string;
  phone?: string;
  phoneType?: string;
  /**
   * 订单信息
   */
  order: {
    [k: string]: unknown;
  };
}

/**
 * @description 根据家庭id获取家庭订单信息
 * https://yapi.yc345.tv/project/2352/interface/api/118791
 * <AUTHOR>
 * @date 2025-01-06
 * @export
 * @param {GetWuhanDatapoolFamilyGetFamilyOrderInfoReqQuery} params
 * @returns {Promise<GetWuhanDatapoolFamilyGetFamilyOrderInfoResBody>}
 */
export const getFamilyOrderApi = (
  params: GetWuhanDatapoolFamilyGetFamilyOrderInfoReqQuery
) => {
  return http.request<GetWuhanDatapoolFamilyGetFamilyOrderInfoResBody>(
    `get`,
    `${baseURL.api}/wuhan-datapool/family/getFamilyOrderInfo`,
    {
      params
    }
  );
};

// 获取用户的家庭组信息
export const getFamilyGroupApi = (params: { userId: string }) => {
  return http.request(
    `get`,
    `${baseURL.api}/wuhan-datapool/info/getFamilyGroupByUserId`,
    {
      params
    }
  );
};
