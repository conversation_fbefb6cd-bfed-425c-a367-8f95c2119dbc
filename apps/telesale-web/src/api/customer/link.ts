/*
 * @Date         : 2024-04-25 12:05:40
 * @Description  : 专属链接相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface IpadReq {
  pad: string; // 平板类型
  isInstallment: number;
  installmentPayType: string | string[];
}

/**
 * @description: 生成加购平板专属链接
 * @param {IpadReq} params
 * @returns {ArrayBuffer}
 */
export const padLinkApi = (data: IpadReq) => {
  return http.request<ArrayBuffer>(
    "post",
    `${baseURL.api}/web/h5link/qrcode/pad`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

/**
 * @description: 生成省钱卡专属链接
 * @param {DiscountsCardReq} params
 * @returns {ArrayBuffer}
 */
export const getDiscountsCardApi = () => {
  return http.request<ArrayBuffer>(
    "post",
    `${baseURL.api}/web/h5link/qrcode/zhuFengPre`,
    {
      responseType: "arraybuffer"
    }
  );
};
