/*
 * @Date         : 2024-12-27 14:33:23
 * @Description  : 核实绑定家庭
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";
export interface PostWuhanDatapoolFamilySearchHoldsReqBodyOther {
  /**
   * 标签页名称 wait_upload/wait_audit/done_audit/expired
   */
  tabName?: string;
  /**
   * 手机号
   */
  holdPhone?: string;
  /**
   * 坐席id
   */
  workerId?: number;
  /**
   * 截止时间-开始
   */
  endTimeStart?: number;
  /**
   * 截止时间-结束
   */
  endTimeEnd?: number;
  /**
   * 页码
   */
  pageIndex?: number;
  /**
   * 条目数
   */
  pageSize?: number;
  /**
   * 排序字段
   */
  sortField?: string;
  /**
   * 排序方式  asc/desc
   */
  sortType?: string;
}

export interface PostWuhanDatapoolFamilySearchHoldsResBody {
  infos?: {
    tabName: string;
    holdPhone: string;
    workerId: number;
    endTime: string;
    holdFamilyId: string;
    HeldFamilyId: string;
    holdFailReason: string;
    verifyStatus: string;
    verifyRejectReason: string;
    audios: {
      audioLink: string;
      attentionTime: string;
    }[];
    images: {
      imageLink: string;
    }[];
  }[];
  list: any[];
  total?: number;
}

/**
 * @description 审核列表
 * https://yapi.yc345.tv/project/1415/interface/api/117748
 * <AUTHOR>
 * @date 2024-12-27
 * @export
 * @param {PostWuhanDatapoolFamilySearchHoldsReqBodyOther} data
 * @returns {Promise<PostWuhanDatapoolFamilySearchHoldsResBody>}
 */
export const getVerifyBindingListApi = (
  data: PostWuhanDatapoolFamilySearchHoldsReqBodyOther
) => {
  return http.request<PostWuhanDatapoolFamilySearchHoldsResBody>(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/searchHolds`,
    {
      data
    }
  );
};

/**
 * @description 上传认证
 */
export const uploadVerifyApi = (data: {
  holdId: string;
  verifyType: string;
  audio?: any[];
  images?: any[];
}) => {
  return http.request<PostWuhanDatapoolFamilySearchHoldsResBody>(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/uploadVerify`,
    {
      data
    }
  );
};

/**
 * @description 审核合并
 */
export const verifyBindApi = (data: {
  holdId: string;
  verifyResult?: string;
  rejectReason?: string;
}) => {
  return http.request<PostWuhanDatapoolFamilySearchHoldsResBody>(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/submitVerify`,
    {
      data
    }
  );
};

/**
 * @description 获取下一个审核信息
 */
export const nextVerifyBindApi = data => {
  return http.request<any>(
    `post`,
    `${baseURL.api}/wuhan-datapool/family/nextVerify`,
    {
      data
    }
  );
};
