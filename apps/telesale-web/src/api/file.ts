import { http } from "../utils/http";
import baseURL from "./url";

export function uploadFile(raw) {
  const formData = new FormData();
  formData.append("file", raw);
  return http.request(
    "post",
    `${baseURL.robot}/admin/file/upload`,
    { data: formData },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
}

/**
 * @description: 文件下载
 * @url /admin/file/download
 */
export function downloadFile(params: { taskId: string }) {
  return http.get(`${baseURL.robot}/admin/file/download`, { params });
}
