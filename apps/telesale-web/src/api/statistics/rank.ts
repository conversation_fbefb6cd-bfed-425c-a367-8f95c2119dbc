/*
 * @Date         : 2024-06-04 18:31:01
 * @Description  : 个人排行榜相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

interface WorkerReq {
  start: number;
  end: number;
  /**
   * 区域 (1 武汉电销 2 长沙电销)
   */
  areaType: number;
  orgIds: number[];
}

interface OldWorkerRes {
  [x: string]: any;
}

/**
 * @description: 老员工营收列表
 * @param {WorkerReq} params
 */
export const getOldWokerRevenueApi = (data: WorkerReq) => {
  return http.request<{ rankAchievementList: OldWorkerRes }>(
    "post",
    `${baseURL.api}/dashboard/aggregation/rank/achievement`,
    {
      data
    }
  );
};

/**
 * @description: 老员工营收导出
 * @returns { Blob }
 */
export const getOldWokerRevenueFileApi = (data: WorkerReq) => {
  return http.request(
    "post",
    `${baseURL.api}/dashboard/aggregation/rank/achievement/export`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};
export interface NewWorkerRes {
  amount: number;
  avgPrice: number;
  orderNum: number;
  worker: string;
  workerid: number;
  mail: string;
  /**
   * 排名
   */
  rankIndex: number;
  /**
   * 奖金
   */
  rewardFee: number;
  stage: {
    id?: number;
    name?: string;
    path?: string;
  };
  team: {
    id?: number;
    name?: string;
    path?: string;
  };
  group: {
    id?: number;
    name?: string;
    path?: string;
  };
  microGroup: {
    id?: number;
    name?: string;
    path?: string;
  };
}

/**
 * @description: 新员工营收列表
 * @param {WorkerReq} params
 */
export const getNewWokerRevenueApi = (data: WorkerReq) => {
  return http.request<{ rankNewAchievementList: NewWorkerRes[] }>(
    "post",
    `${baseURL.api}/dashboard/aggregation/rank/new_achievement`,
    {
      data
    }
  );
};

/**
 * @description: 新员工营收导出
 * @returns { Blob }
 */
export const getNewWokerRevenueFileApi = (data: WorkerReq) => {
  return http.request(
    "post",
    `${baseURL.api}/dashboard/aggregation/rank/new_achievement/export`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};
