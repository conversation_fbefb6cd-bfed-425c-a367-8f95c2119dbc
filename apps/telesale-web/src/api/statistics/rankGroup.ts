/*
 * @Date         : 2025-02-26 18:01:12
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

interface TabSettingReq {
  id?: number;
  pageName?: string;
  action: string;
  tabName?: string;
  orgList?: number[];
}

/**
 * @description: 老员工营收列表
 * @param {TabSettingReq} params
 */
export const tabSettingApi = (data: TabSettingReq) => {
  return http.request(
    "post",
    `${baseURL.api}/web/organization/ranking/org_setting`,
    {
      data
    }
  );
};
