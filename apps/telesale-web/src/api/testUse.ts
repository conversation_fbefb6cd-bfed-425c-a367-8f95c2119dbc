import { http } from "../utils/http";
import baseURL from "./url";

//模拟呼叫记录数据
export const callPush = params => {
  return http.request("get", `${baseURL.api}/web/repeater/record/1`, {
    params
  });
};

//模拟APP推送数据
export const dataPush = data => {
  return http.request("post", `${baseURL.api}/web/customer/push`, {
    data
  });
};

//修改用户注册渠道号
export const setUserBaseMsg = data => {
  return http.request("put", `${baseURL.api}/web/user/info`, {
    data
  });
};

//检查用户是否满足公海池过滤条件
export const checkUserPass = data => {
  return http.request("post", `${baseURL.api}/web/leads/active`, {
    data
  });
};
