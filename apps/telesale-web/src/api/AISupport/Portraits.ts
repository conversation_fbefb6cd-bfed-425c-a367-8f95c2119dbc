import { http } from "../../utils/http";
import baseURL from "../url";

export function getCustomerState(params: {
  userId: string;
  phone: string;
  city: string;
  regionCode: string;
}): Promise<ReturnValue<any>> {
  return http.request("post", `${baseURL.robot}/admin/customer`, {
    data: params
  });
}

/**
 * @description: 学情分析
 */
export function getLearningAnalyze(params: {
  userId: string;
  startTime: number;
  endTime: number;
}) {
  return http.request(
    "post",
    `${baseURL.robot}/admin/learning/analyze`,
    {
      data: params
    },
    {
      timeout: 99999999
    }
  );
}
