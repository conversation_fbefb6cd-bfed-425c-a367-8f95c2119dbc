import { http } from "../../utils/http";
import baseURL from "../url";
import axios from "axios";

export function getCoversation(params: {
  pages: number;
  pageSize: number;
  benginTime?: string;
  endTime?: string;
  actionId?: string;
  workerId?: string;
  minDuration?: number;
  maxDuration?: number;
  infoUUID?: string;
  filterZero?: boolean; // 是否过滤为空的会话
}): Promise<ReturnValue<any>> {
  return http.request("get", `${baseURL.robot}/admin/conversation`, { params });
}

/**
 * @description: 建立ai会话
 * @param {object} data
 */
export function createCoversation(data: {
  workerId: number;
  actionId: string;
  version: string;
}): Promise<ReturnValue<any>> {
  return http.request("post", `${baseURL.robot}/admin/conversation`, { data });
}

export interface SopRes {
  score: number;
  sops: {
    step: string;
    reason: string;
    createdAt: string;
    updatedAt: string;
    status: string;
    appraisal: string;
    manual: boolean;
    score: number;
  }[];
}

export interface SopReq {
  role: string;
  content: string;
  start: string;
}

/**
 * @description: 发送sop消息
 * @param {object} par
 */
export function sendSop(par: {
  workerId: number;
  messages: SopReq[];
  actionId: string;
}) {
  return http.post(`${baseURL.robot}/admin/conversation/sop`, {
    data: { conversation: par }
  });
}

export interface Tip {
  content: string;
  score: number;
  question: string;
}
/**
 * @description: 发送智能话术
 * @param {object} par
 */
export function sendTips(par: {
  workerId: number;
  messages: SopReq[];
  actionId: string;
}) {
  return http.post(`${baseURL.robot}/admin/conversation/tips`, {
    data: { conversation: par }
  });
}

/**
 * @description: 获取会话详情
 * @param {*} id
 */
export function getCoversationDetail(id: string) {
  return http.get(`${baseURL.robot}/admin/conversation/${id}`);
}

/**
 * @description: AI质检
 * @param {string} id
 * @param {SopReq} sop
 */
export function AIQuality(id: string, sop: SopReq[]) {
  return http.post(`${baseURL.robot}/admin/conversation/${id}`, {
    data: { sop }
  });
}

export interface Config {
  sopList: {
    name: string;
    desc: string[];
    score: number;
    questions: any[];
  }[];
  keywordList: {
    key: string;
    name: string;
    desc: string;
  }[];
}

/**
 * @description: 获取基本信息
 */
export function getConversationConfig(): Promise<
  ReturnValue<{
    config: Config;
  }>
> {
  return http.get(`${baseURL.robot}/admin/conversation/config`);
}

/**
 * @description: 发送finish信息
 * @param {object} data
 */
export function finishCoversation(data: {
  workerid: number;
  actionId: string;
  skipSummary: boolean;
}) {
  return http.post(`${baseURL.robot}/admin/conversation/finish`, {
    data
  });
}

/**
 * @description: 更新对话总结
 */
export function updateSummary(par: {
  conversation: {
    id: string;
    summary: {
      content: string;
    };
  };
}) {
  return http.post(`${baseURL.robot}/admin/conversation/summary`, {
    data: par
  });
}

/**
 * @description: 推送消息
 */
export function pushMsg(par: {
  workerid: number;
  body: any[];
  finish: boolean;
  actionId: string;
}) {
  return http.post(`${baseURL.robot}/admin/conversation/push`, {
    data: par
  });
}

/**
 * @description: sop重试
 * @param {*} par
 */
export function retrySop(par) {
  return http.post(`${baseURL.robot}/admin/conversation/retry/sop`, {
    data: par
  });
}

/**
 * @description: summary重试
 * @param {*} par
 */
export function retrySummary(par) {
  return http.post(`${baseURL.robot}/admin/conversation/retry/summary`, {
    data: par
  });
}

/**
 * @description: 获取软电话版本号
 */
export function getPhoneVersion() {
  return axios.get(`http://127.0.0.1:9876/version`);
}
