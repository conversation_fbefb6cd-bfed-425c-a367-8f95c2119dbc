/*
 * @Date         : 2024-04-12 11:37:47
 * @Description  : 外呼团队管理apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface CallTeam {
  teamEn: string;
  teamName: string;
  action: "add" | "update" | "delete";
  channelConf: ChannelConf[];
}

export interface ChannelConf {
  id?: number;
  channelEn: string;
  channelName: string;
  accessId: string;
  secret: string;
  /**
   * @description: 进行什么操作，新增，编辑，删除
   */
  action: "add" | "update" | "delete";
}

interface ResData {
  errCode: number;
  errMsg: string;
}

/**
 * @description: 获取团队配置列表
 * @returns {data}
 */
export const getCallTeamApi = () => {
  return http.request<{
    teamConf: CallTeam[];
    list: any;
  }>("get", `${baseURL.api}/wuhan-callphone/repeater/open/conf/list`);
};

/**
 * @description: 获取团队配置详情
 * @param {teamEn} params
 * @returns {AppealOrderRes}
 */
export const getCallTeamInfoApi = (params: { teamEn: string }) => {
  return http.request<
    {
      teamConf: CallTeam;
    } & ResData
  >("get", `${baseURL.api}/wuhan-callphone/repeater/open/conf/detail`, {
    params
  });
};

/**
 * @description: 团队配置增删改
 * @data {CallTeam} data
 */
export const operateCallTeamApi = (data: CallTeam) => {
  return http.request<ResData>(
    "post",
    `${baseURL.api}/wuhan-callphone/repeater/open/conf/operate`,
    {
      data
    }
  );
};
