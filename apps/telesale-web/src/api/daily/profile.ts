/*
 * @Date         : 2024-05-09 14:54:32
 * @Description  : 资料管理相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface ProfileReq {
  id?: number;
  materialName: string;
  materialPath: string;
  materialFile: string;
}

export interface ProfileInfo {
  id: number;
  materialName: string;
  materialPath: string;
  materialFile: string;
}

/**
 * @description: 获取专属资料列表
 * @returns {ProfileInfo[]} data
 */
export const getProfileApi = () => {
  return http.request<{
    list: ProfileInfo[];
  }>("get", `${baseURL.api}/wuhan-datapool/materials/list`);
};

/**
 * @description: 获取专属资料详情
 * @param { number } id
 * @returns {ProfileInfo} data
 */
export const getProfileInfoApi = (id: number) => {
  return http.request<ProfileInfo>(
    "get",
    `${baseURL.api}/wuhan-datapool/materials/detail`,
    {
      params: {
        id
      }
    }
  );
};

/**
 * @description: 新增专属资料
 * @param {ProfileReq} data
 * @returns {ProfileInfo[]} data
 */
export const addProfileApi = (data: ProfileReq) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/materials/add`, {
    data
  });
};

/**
 * @description: 编辑专属资料
 * @param {ProfileReq} data
 * @returns {ProfileInfo[]} data
 */
export const updateProfileApi = (data: ProfileReq) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/materials/update`,
    {
      data
    }
  );
};

/**
 * @description: 删除专属资料列表
 */
export const deleteProfileApi = (id: number) => {
  return http.request("get", `${baseURL.api}/wuhan-datapool/materials/delete`, {
    params: {
      id
    }
  });
};
