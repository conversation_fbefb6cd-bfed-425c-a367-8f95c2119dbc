/*
 * @Date         : 2024-05-09 14:54:32
 * @Description  : 资料管理相关api
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface ProfileReq {
  id?: number;
  materialName?: string;
  materialPath?: string;
  materialFile?: string;
  belongGood?: number[]; // 商品类型
  stage?: number[]; // 学段
  subject?: number[]; // 学科
  materialNameBatch?: string[]; // 资料名称
  materialFileBatch?: string[]; // 资料文件
  materialPathBatch?: string[]; // 资料路径
}

export interface ProfileInfo {
  id: number;
  materialName?: string;
  materialPath?: string;
  materialFile?: string;
  belongGood?: number[]; // 商品类型
  stage?: number[]; // 学段
  subject?: number[]; // 学科
  materialNameBatch?: string[]; // 资料名称
  materialFileBatch?: string[]; // 资料文件
  materialPathBatch?: string[]; // 资料路径
}

/**
 * @description: 获取专属资料列表
 * @returns {ProfileInfo[]} data
 */
export const getProfileApi = data => {
  return http.request<{
    list: ProfileInfo[];
    total: number;
  }>("post", `${baseURL.api}/wuhan-datapool/materials/list`, { data });
};

/**
 * @description: 获取专属资料详情
 * @param { number } id
 * @returns {ProfileInfo} data
 */
export const getProfileInfoApi = (id: number) => {
  return http.request<ProfileInfo>(
    "get",
    `${baseURL.api}/wuhan-datapool/materials/detail`,
    {
      params: {
        id
      }
    }
  );
};

/**
 * @description: 新增专属资料
 * @param {ProfileReq} data
 * @returns {ProfileInfo[]} data
 */
export const addProfileApi = (data: ProfileReq) => {
  return http.request<{
    errMaterialName: string[];
  }>("post", `${baseURL.api}/wuhan-datapool/materials/add`, {
    data
  });
};

/**
 * @description: 编辑专属资料
 * @param {ProfileReq} data
 * @returns {ProfileInfo[]} data
 */
export const updateProfileApi = (data: ProfileReq) => {
  return http.request<{
    errMaterialName: string[];
  }>("post", `${baseURL.api}/wuhan-datapool/materials/update`, {
    data
  });
};

/**
 * @description: 删除专属资料列表
 */
export const deleteProfileApi = (id: number) => {
  return http.request("get", `${baseURL.api}/wuhan-datapool/materials/delete`, {
    params: {
      id
    }
  });
};

/**
 * @description: 批量删除专属资料列表
 */
export const batchDeleteProfileApi = (id: number[]) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/materials/batch_delete`,
    {
      data: {
        id
      }
    }
  );
};
