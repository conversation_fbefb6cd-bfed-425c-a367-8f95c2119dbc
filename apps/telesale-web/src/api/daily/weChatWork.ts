/*
 * @Date         : 2024-06-03 17:06:51
 * @Description  : 企微管理相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface WeChatInfo {
  id: number;
  /** 企业ID	 */
  corpId: string;
  /** 企业微信账号名称 */
  name: string;
  /** 自建应用的ID */
  agentId: string;
  /** 自建应用的Secret */
  agentSecret: string;
  /** 企业微信通讯录的Secret */
  contactSecret: string;
  /** 企业微信客户联系的Secret */
  customSecret: string;
  tag: string[] | any[];
  remark: string;
  welcome: string;
}

/**
 * @description: 获取企微列表
 * @returns {WeChatInfo[]} data
 */
export const getWeChatListApi = () => {
  return http.request<{
    list: WeChatInfo[];
  }>("get", `${baseURL.api}/wuhan-worker/account/ListWecomAccount`);
};

/**
 * @description: 新增企微
 * @param {WeChatInfo} data
 */
export const addWeChatApi = (data: WeChatInfo) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/account/CreateWecomAccount`,
    {
      data
    }
  );
};

/**
 * @description: 编辑企微
 * @returns {WeChatInfo} data
 */
export const updateWechatApi = (data: WeChatInfo) => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-worker/account/UpdateWecomAccount`,
    {
      data
    }
  );
};

/**
 * @description: 获取企微详情
 * @param {number} id
 * @returns {WeChatInfo[]} data
 */
export const getWeChatInfoApi = (params: { id: number }) => {
  return http.request<WeChatInfo>(
    "get",
    `${baseURL.api}/wuhan-worker/account/GetWecomAccount`,
    { params }
  );
};

export interface WorkerListInfo {
  accountId: number;
  cropId: string;
  workerId: number;
  userId: string;
  fetchCustomerUrl: string;
  fetchCustomerUrlId: string;
  accountName: string;
  acceptTraffic: number;
}

/**
 * @description: 获取坐席的企业微信账号
 * @param {number} workerId
 * @returns {WeChatInfo[]} data
 */
export const getWorkerWeChatApi = (params: { workerId: number }) => {
  return http.request<{
    list: WorkerListInfo[];
  }>("post", `${baseURL.api}/wuhan-worker/wuhan-worker/worker/getWecomWorker`, {
    params
  });
};

export interface AccpetQuery {
  workerId: number;
  accountId: number;
  acceptTraffic: number;
}

/**
 * @description: 切换承接流量开关
 * @param {AccpetQuery} data
 */
export const changeAcceptApi = (data: AccpetQuery) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/worker/changeWecomTraffic`,
    {
      data
    }
  );
};

export interface UnbindWecomQuery {
  workerId: number;
  accountId: number;
}

/**
 * @description: 解绑企微
 * @param {UnbindWecomQuery} data
 */
export const unbindWecomApi = (data: UnbindWecomQuery) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/worker/unbindWecom`,
    {
      data
    }
  );
};
