/*
 * @Date         : 2024-03-18 15:07:29
 * @Description  : 申诉相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * @description: 申诉单是否存在
 * @param {number} orderId
 * @returns {AppealOrderRes}
 */
export const getAppealExistApi = (params: {
  orderId: number;
  excludeId: number;
}) => {
  return http.request<{
    exist: boolean;
  }>("get", `${baseURL.api}/sync-order/appeal/exist`, {
    params
  });
};
