import { http } from "../utils/http";
import baseURL from "./url";

export interface WorkerInfo {
  workerName: string;
  workerid: string;
  phone: string;
  onionid: string;
  firstValidDial: string;
  userid: string;
  stage: string;
  grade: string;
  featureTags: number[];
}
//查询手机号对应的坐席归属
export const phoneInfo = data => {
  return http.request<WorkerInfo>(
    "post",
    `${baseURL.api}/web/customer/phone/info`,
    {
      data
    }
  );
};

//B转C用户
export const findBtoC = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/customer/b2c`,
    {
      params
    }
  );
};
//查询转介绍推荐人-无限制
export const findReferral = params => {
  return http.request<{
    onionId: string;
    grade: string;
    userId: string;
  }>("get", `${baseURL.api}/wuhan-datapool/admin/referral/user`, {
    params
  });
};

//线索溯源
export const findClueHistory = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/customer/history`,
    {
      params
    }
  );
};

// 获取专属链接详情
export const getLinkInfoApi = params => {
  return http.request("get", `${baseURL.api}/web/h5link/detail`, {
    params
  });
};

//获取二维码
export const getErCode = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/h5link/qrcode`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

//生成新禧商品二维
export const createErcCode = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/h5link/qrcode/xinxi`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

//获取大会场二维码
export const getErCodeRoom = () => {
  return http.request("post", `${baseURL.api}/web/meetroom/qrcode`, {
    responseType: "arraybuffer"
  });
};
//查询商城课程列表
export const getCourseList = data => {
  return http.request("post", `${baseURL.api}/web/h5link/course/list`, {
    data
  });
};

//添加商品链接
export const addLink = data => {
  return http.request("post", `${baseURL.api}/web/h5link/add`, {
    data
  });
};

// 编辑课程标签
export const updateLinkApi = data => {
  return http.request("post", `${baseURL.api}/web/h5link/update`, {
    data
  });
};

// 获取课程标签列表
export const getTagListApi = () => {
  return http.request("get", `${baseURL.api}/sync-order/course/tag/list`);
};

// 创建课程标签
export const addTagApi = data => {
  return http.request("post", `${baseURL.api}/sync-order/course/tag`, {
    data
  });
};

// 删除课程标签
export const delTagApi = params => {
  return http.request("delete", `${baseURL.api}/sync-order/course/tag`, {
    params
  });
};

//移除商品链接
export const delLink = params => {
  return http.request("get", `${baseURL.api}/web/h5link/delete`, {
    params
  });
};

//同步商品链接
export const syncLink = params => {
  return http.request("get", `${baseURL.api}/web/h5link/sync`, {
    params
  });
};

//查询商品列表
export const getLinkList = data => {
  return http.request("post", `${baseURL.api}/web/h5link/list`, {
    data
  });
};

interface ClueInfoRes {
  id: number;
  createdAt: string;
  updatedAt: string;
  infoUuid: string;
  workerid: number;
  source: string;
  userid: string;
  usertype: number;
  isLowQuality: number;
  userExpire: number;
  lastExpire: number;
  topicId: string;
  videoid: string;
  phone: string;
  nickname: string;
  receiveTime: number;
  extend: string;
  firstDialDuration: number;
  firstValidDial: number;
  note: string;
  grade: string;
  stage: string;
  isVIP: number;
  authList: string;
  regTime: string;
  onionid: string;
  intention: number;
  department: number;
  sender: string;
  historyAmount: HistoryAmount | number;
  label: string;
  tag: string;
  orderNum: number;
  isExcellent: number;
  featureTags: number[];
  WeComOpenId: string;
  groupId0: number;
  groupId1: number;
  groupId2: number;
  groupId3: number;
  groupId4: number;
  role: string;
  lastDial: number;
  sourceDetail: string;
  siteId: number;
  teamId: number;
  lastActiveTime: number;
  haveCall: number;
  callCount: number;
  callDuration: number;
  callState: number;
  newExam: boolean;
  channel: string;
  isAddWechat?: any;
  isPadUser: boolean;
}

interface HistoryAmount {
  s: number;
  e: number;
  c: number[];
}

//客户池列表
export const getOngingList = data => {
  return http.request<{
    list: ClueInfoRes[];
    total: number;
  }>("post", `${baseURL.api}/web/customer/list`, {
    data
  });
};

interface DoneInfo {
  id: number;
  createdAt: string;
  updatedAt: string;
  infoUuid: string;
  workerid: number;
  source: string;
  userid: string;
  usertype: number;
  isLowQuality: number;
  userExpire: number;
  lastExpire: number;
  topicId: string;
  videoid: string;
  phone: string;
  nickname: string;
  receiveTime: number;
  extend: string;
  firstDialDuration: number;
  firstValidDial: number;
  note: string;
  grade: string;
  stage: string;
  isVIP: number;
  authList: string;
  regTime: string;
  onionid: string;
  intention: number;
  department: number;
  sender: string;
  historyAmount: number;
  label: string;
  tag: string;
  orderNum: number;
  isExcellent: number;
  featureTags: number[];
  WeComOpenId: string;
  groupId0: number;
  groupId1: number;
  groupId2: number;
  groupId3: number;
  groupId4: number;
  role: string;
  lastDial: number;
  sourceDetail: string;
  siteId: number;
  teamId: number;
  lastActiveTime: number;
  lastDealing: number;
  haveCall: number;
  callCount: number;
  callDuration: number;
  callState: number;
  newExam: boolean;
  lastPaidTime: number;
  openCount0: number;
  openCount1: number;
  openCount3: number;
  openCount7: number;
  openCount14: number;
  initialPhone: string;
  qrCodeChannelID: number;
  provinceCode: string;
  cityCode: string;
  districtCode: string;
  good: Good;
  payTime: number;
  orderid: string;
  amount: number;
  isPadUser: boolean;
  openCount: number;
  description: string;
  province: string;
  city: string;
  district: string;
  cityClass: string;
}

interface Good {
  agent: Agent;
  courseTime: CourseTime;
  stopLossAmount: number;
  paymentPlatform: string[];
  buyOnce: boolean;
  buyLimit: number;
  status: string;
  groupList: string[];
  images: any[];
  isEnabled: boolean;
  isDeleted: boolean;
  orderImages: any[];
  presents: Presents;
  recommended: boolean;
  promotionInfo?: any;
  name: string;
  description: string;
  amount: number;
  originalAmount: number;
  additionalData: AdditionalData;
  createdBy: string;
  note: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  __note_2020_03_11: string;
  __note_2020_03_11_Date: string;
  skuList: SkuList[];
  skuSize: number;
  link: string;
  _id: string;
  category: string;
  content: string;
}

interface SkuList {
  skuId: string;
  amount: number;
  extraParams: ExtraParams;
  sku: Sku;
}

interface Sku {
  isEnabled: boolean;
  images: any[];
  name: string;
  distributor: Distributor;
  amount: number;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  id: string;
}

interface Distributor {
  kind: string;
  params: Params;
}

interface Params {
  quantity: number;
  stage: string;
  subject: string;
  addTime: number;
}

interface ExtraParams {
  curriculumId: string;
}

interface AdditionalData {
  type: string;
  discountProductIdMap: any;
  productId: string;
}

interface Presents {
  qqCoin: number;
  textbook: number;
  dress: number;
  ycCoin: number;
}

interface CourseTime {}

interface Agent {
  discountedPrices?: any;
  settlementPrices?: any;
}

//已成交客户列表
export const buyList = data => {
  return http.request<{
    list: DoneInfo[];
    total: number;
  }>("post", `${baseURL.api}/web/customer/buy/list`, {
    data
  });
};

//线索锁定状态批量查询
export const findLock = data => {
  return http.request("post", `${baseURL.api}/web/customer/lock/query`, {
    data
  });
};

//删除人工录入的线索
export const delManual = params => {
  return http.request("delete", `${baseURL.api}/web/customer/remove`, {
    params
  });
};

//绑定账号
export const bindAccount = data => {
  return http.request("post", `${baseURL.api}/web/customer/bind/account`, {
    data
  });
};

//锁定线索
export const lockClue = data => {
  return http.request("post", `${baseURL.api}/web/customer/lock`, {
    data
  });
};

//解绑线索
export const unlockClue = data => {
  return http.request("post", `${baseURL.api}/web/customer/unlock`, {
    data
  });
};

//人工录入
export const manualAdd = data => {
  return http.request("post", `${baseURL.api}/web/admin/customer/add`, {
    data
  });
};

//申请客户线索
export const applyClue = () => {
  return http.request("get", `${baseURL.api}/web/customer/apply`);
};

//冷静期--上传
export const coolUpload = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/customer/duration/upload`,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//冷静期--导入
export const coolImport = data => {
  return http.request("post", `${baseURL.api}/web/customer/duration/migrate`, {
    data
  });
};

//跟单记录
export const documentaryRecord = data => {
  return http.request("post", `${baseURL.api}/web/customer/call/record/list`, {
    data
  });
};

//跟单记录-添加
export const documentaryAdd = data => {
  return http.request("post", `${baseURL.api}/web/customer/call/record/add`, {
    data
  });
};

//获取推荐数据
export const getRecommend = params => {
  return http.request("get", `${baseURL.api}/web/recommend`, { params });
};

//推荐池数目
export const getRecommendCount = params => {
  return http.request("get", `${baseURL.api}/web/recommend/count`, { params });
};

//推荐机会列表
export const recommendList = data => {
  return http.request("post", `${baseURL.api}/web/recommend/list`, {
    data
  });
};

//获取意向度字典表
export const intention = () => {
  return http.request("get", `${baseURL.api}/web/customer/intention/list`);
};

// 根据坐席ID获取活跃度列表
export const weComActiveApi = (
  params: { count?: number; workerId?: number; accountId: number } & PageInfo
) => {
  return http.request<{
    list: {
      id: number;
      nicek: string;
      remark: string;
      recentlyActiveDay: number;
      lastActiveAt: string;
    }[];
    total: number;
  }>("get", `${baseURL.api}/wuhan-miniprogram/wechatUserActiveRank`, {
    params
  });
};

export interface AllocationRes {
  userId: string;
  allocations: {
    teamId: number;
    state: string;
    startTime: string;
    endTime: string;
  }[];
}

// 获取服务期
export const getAllocationApi = (params: {
  phone?: string;
  onionId?: string;
}) => {
  return http.request<AllocationRes>(
    "get",
    `${baseURL.api}/wuhan-datapool/info/allocation/get`,
    {
      params
    }
  );
};
