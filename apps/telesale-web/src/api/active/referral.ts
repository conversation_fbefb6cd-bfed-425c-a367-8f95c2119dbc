/*
 * @Date         : 2024-03-27 12:05:35
 * @Description  : 转介绍相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * @description: 下载阿拉丁转介绍海报
 * @param {userId} data
 */
export const getAladdinPosterApi = (data: {
  promotionIds: string[];
  workerId: number;
  userId: string;
  phone: string;
}) => {
  return http.request<{
    list: {
      url: string;
      posterName: string;
    }[];
  }>("post", `${baseURL.api}/web/alading/promotion/poster/download`, {
    data
  });
};

/**
 * @description: 获取上架的海报
 * @param {userId} params
 */
export const getPosterQualityApi = (params: { userId: string }) => {
  return http.request<{
    url: string;
  }>("get", `${baseURL.api}/alading/promotion/poster/quality`, {
    params
  });
};
