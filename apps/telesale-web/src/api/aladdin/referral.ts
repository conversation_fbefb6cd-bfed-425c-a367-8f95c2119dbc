/*
 * @Date         : 2024-03-27 11:56:46
 * @Description  : 阿拉丁转介绍列表的apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";
import { PhoneInfoReq } from "/@/types/customer/beyond";

export type AladdinReferralReq = PhoneInfoReq;

export interface AladdinReferralRes {
  id: number;
  phone: number;
  onionId: string;
  num: number;
  amount: number;
  orederNumber: number;
}

/**
 * @description: 获取转介绍列表
 * @param {AladdinReferralReq} params
 */
export const getAladdinReferralApi = (
  params: AladdinReferralReq & PageInfo
) => {
  return http.request<{
    list: AladdinReferralRes[];
    total: number;
  }>("get", `${baseURL.api}/web/alading/list_relation_basic_info`, {
    params
  });
};

/**
 * @description: 获取裂变用户详情
 * @param {object} params
 */
export const getFissionInfoApi = (params: { id: number }) => {
  return http.request<Omit<AladdinReferralRes, "orederNumber">[]>(
    "get",
    `${baseURL.api}/web/alading/list_relation_detail_info`,
    {
      params
    }
  );
};

/**
 * @description: 获取转介绍人信息
 * @param {object} params
 */
export const getAladdinReferralUserApi = (params: { userId: number }) => {
  return http.request<{
    userId: string;
    onionId: string;
    grade: string;
  }>("get", `${baseURL.api}/web/alading/get_referral_user_info`, {
    params
  });
};
