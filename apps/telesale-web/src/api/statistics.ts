import { http } from "../utils/http";
import baseURL from "./url";

//营收
export const getRevenue = data => {
  return http.request("post", `${baseURL.api}/web/analy/order/list`, {
    data
  });
};

//绩效
export const getSalary = data => {
  return http.request("post", `${baseURL.api}/web/analy/salary/list`, {
    data
  });
};

//导出绩效
export const salaryExport = data => {
  return http.request("post", `${baseURL.api}/web/analy/salary/export`, {
    data
  });
};

//查询坐席线索权重贡献分
export const scoreFind = data => {
  return http.request("post", `${baseURL.api}/web/worker/lastmonth/score`, {
    data
  });
};

//奖金明细
export const moneyDetails = () => {
  return http.request("get", `${baseURL.api}/web/rank/workerQuota/list`);
};

//坐席接收统计导出
export const workerExport = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/analy/worker/receive/export`,
    {
      data
    }
  );
};

//客服推送导出
export const kfExport = data => {
  return http.request("post", `${baseURL.api}/web/analy/kf/send/export`, {
    data
  });
};

//统计客服推送的线索
export const servicePush = data => {
  return http.request("post", `${baseURL.api}/web/analy/kf/send/kf/info`, {
    data
  });
};

//统计坐席接收的客服线索
export const agentReception = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/analy/worker/receive/kf/info`,
    {
      data
    }
  );
};

//推送详情列表
export const pushInfo = data => {
  return http.request("post", `${baseURL.api}/web/query/kf/push/info`, {
    data
  });
};

//转介绍团队业绩
export const getTransferGroupReferral = params => {
  return http.request("get", `${baseURL.api}/web/stat/referral/group`, {
    params
  });
};

//转介绍个人业绩
export const getTransferPersonReferral = params => {
  return http.request("get", `${baseURL.api}/web/stat/referral/worker`, {
    params
  });
};

//转介绍个人业绩总计
export const getTransferPersonReferralTotal = params => {
  return http.request("get", `${baseURL.api}/web/stat/referral/worker/total`, {
    params
  });
};

//转介绍个人业绩详情
export const getTransferPersonReferralDetail = params => {
  return http.request("get", `${baseURL.api}/web/stat/referral/worker/detail`, {
    params
  });
};

//查询挽单统计列表
export const rescueOrder = params => {
  return http.request("get", `${baseURL.api}/web/rescueOrder/statistics`, {
    params
  });
};

//获取挽单统计详情
export const rescueOrderDetails = params => {
  return http.request("get", `${baseURL.api}/web/rescueOrder/getList`, {
    params
  });
};

//导出挽单统计列表
export const rescueOrderExport = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/rescueOrder/statistics/export`,
    {
      params
    },
    {
      responseType: "blob"
    }
  );
};

//获取当配统计
export const getMatch = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/workerAllocateInfo/aggregation`,
    {
      params
    }
  );
};

//导出当配统计
export const matchExport = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/workerAllocateInfo/aggregation/export`,
    {
      params
    },
    {
      responseType: "blob"
    }
  );
};

//获取当配外呼统计
export const getMatchOutbound = data => {
  return http.request(
    "post",
    `${baseURL.api}/dashboard/aggregation/${data.type}`,
    {
      data
    }
  );
};

//查询评论列表
export const getListComment = params => {
  return http.request("get", `${baseURL.api}/customer-service/comment`, {
    params
  });
};

//导出评论
export const commentExport = params => {
  return http.request(
    "get",
    `${baseURL.api}/customer-service/comment/export`,
    {
      params
    },
    {
      responseType: "blob"
    }
  );
};
