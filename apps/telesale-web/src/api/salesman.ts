import QS from "qs";
import { http } from "../utils/http";
import baseURL from "./url";
import {
  PerformanceWorker,
  SalesmanDetail,
  SalesmanInfoRes,
  SalesmanQuery,
  TrainingInfo
} from "/@/types/salesman/index";

// 获取人员列表
export const getSalesmanListApi = (params: SalesmanQuery & PageInfo) => {
  return http.request<SalesmanInfoRes>(
    "get",
    `${baseURL.api}/wuhan-worker/deliveryPosition`,
    {
      params
    }
  );
};

// 获取培训数据
export const getTableRecordApi = (params: { talentId: number }) => {
  return http.request<TrainingInfo>(
    "get",
    `${baseURL.api}/wuhan-worker/training`,
    {
      params
    }
  );
};

// 获取坐席每个月的业绩
export const getPerformanceApi = (params: { workerId: number }) => {
  return http.request<{ list: PerformanceWorker[] }>(
    "get",
    `${baseURL.api}/wuhan-worker/deliveryPositionPerformance`,
    {
      params
    }
  );
};

// 获取详情
export const getSalesmanInfoApi = (id: string) => {
  return http.request<SalesmanDetail>(
    "get",
    `${baseURL.api}/wuhan-worker/deliveryPosition/${id}`
  );
};

interface JobNameQuery {
  id: number;
  jobName: string;
}

// 更新面试职位
export const updateJobNameApi = (data: JobNameQuery) => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-worker/deliveryPosition/${data.id}`,
    {
      data
    }
  );
};

interface UpdateExtend {
  deliveryPositionId: number;
  gender?: number;
  huJiLocation?: string;
  address?: string;
  maritalStatus?: number;
  commutingDistance?: string;
}

// 更新拓展信息
export const updateExtendApi = (data: UpdateExtend) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/deliveryPosition/extend`,
    {
      data
    }
  );
};

interface UpdataInterview {
  feiShuEHRApplicationId: string;
  status?: number;
  interviewer?: string;
}

// 更新面试职位
export const updateInterviewApi = (data: UpdataInterview) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/deliveryPosition/interview`,
    {
      data
    }
  );
};

interface UpdataInterview {
  id: number;
  status?: number;
  interviewer?: string;
}

// 导出人员
export const exportExcelApi = (
  params: SalesmanQuery & { columns: string[] }
) => {
  return http.request<any>(
    "get",
    `${baseURL.api}/wuhan-worker/deliveryPosition/excel/export`,
    {
      params,
      paramsSerializer: function (params) {
        return QS.stringify(params, { indices: false });
      }
    },
    {
      responseType: "blob"
    }
  );
};
