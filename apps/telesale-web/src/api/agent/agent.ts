/*
 * @Date         : 2024-02-29 18:41:50
 * @Description  : 坐席相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";
import { LeafNodeQuery, LeafNodeRes } from "/@/types/agent/agent";

/**
 * @description: 查询叶子结点非离职坐席
 * @param {LeafNodeQuery} params
 * @returns {LeafNodeRes}
 */
export const getLeafNodeApi = (params: LeafNodeQuery) => {
  return http.request<{
    workers: LeafNodeRes[];
  }>("get", `${baseURL.api}/wuhan-worker/wuhan-worker/relationship/leaf_node`, {
    params
  });
};
