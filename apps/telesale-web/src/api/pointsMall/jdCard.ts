/*
 * @Date         : 2025-02-27 11:46:00
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "/@/utils/http";
import baseURL from "../url";

/**
 * @description: 导入京东卡
 */
export const jdCardListApi = () => {
  return http.request<{
    list: any[];
    total: number;
  }>("get", `${baseURL.api}/wuhan-marketing/jdCard/log/list`);
};

/**
 * @description: 获取京东卡数据
 */
export const importJdCardApi = (data: FormData) => {
  return http.request("post", `${baseURL.api}/wuhan-marketing/jdcard/upload`, {
    data
  });
};

/**
 * @description: 导入京东卡
 */
export const importJdCarDatadApi = (data: any) => {
  return http.request("post", `${baseURL.api}/wuhan-marketing/jdCard/add`, {
    data
  });
};

export interface JdCardInventoryResBody {
  inventoryInfos?: {
    /**
     * 规格名称
     */
    name?: string;
    /**
     * 已兑换
     */
    hasExchange?: number;
    /**
     * 未兑换
     */
    noExchange?: number;
  }[];
}

/**
 * @description 获取库存信息
 * https://yapi.yc345.tv/project/1415/interface/api/122684
 * <AUTHOR>
 * @date 2025-02-27
 * @export
 * @returns {Promise<JdCardInventoryResBody>}
 */
export const getJdCardInventoryApi = () => {
  return http.request<JdCardInventoryResBody>(
    `get`,
    `${baseURL.api}/wuhan-marketing/jdCard/inventory`,
    {}
  );
};
