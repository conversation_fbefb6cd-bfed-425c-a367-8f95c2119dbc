/*
 * @Date         : 2024-03-01 15:40:45
 * @Description  : 积分管理API
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";
import { UpdateStatusReq } from "/@/types/pointsMall/pointsManage";

/**
 * @description: 积分管理列表
 * @param {*} params
 */
export const getListIntegral = params => {
  return http.request("get", `${baseURL.api}/web/point`, { params });
};

/**
 * @description: 更新用户积分状态
 * @param {UpdateStatusReq} data
 */
export const updateStatusApi = (data: UpdateStatusReq) => {
  return http.request("post", `${baseURL.api}/web/point/ban`, {
    data
  });
};

export interface PointOperationReq {
  userId: string;
  changeType: number;
  transferOnionId?: string;
  point: number;
  reason: string;
}

export interface PointOperationRes extends PointOperationReq {
  createdAt: string;
  operatorId: string;
}

/**
 * @description: 积分操作
 * @param {PointOperationReq} data
 */
export const pointOperationApi = (data: PointOperationReq) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-marketing/point/pointManage`,
    {
      data
    }
  );
};

/**
 * @description: 积分操作记录
 * @param {string} userId
 */
export const getPointRecordApi = (params: { userId: string }) => {
  return http.request<{
    list: PointOperationRes[];
  }>("get", `${baseURL.api}/wuhan-marketing/point/listPointManage`, {
    params
  });
};
