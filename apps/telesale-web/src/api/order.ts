import { http } from "../utils/http";
import baseURL from "./url";

//获取下一条申诉单
export const getNextAppeal = params => {
  return http.request("get", `${baseURL.api}/sync-order/appeal/next`, {
    params
  });
};

//申诉列表
export const getListAppeal = data => {
  return http.request("post", `${baseURL.api}/web/appeal/list`, { data });
};

//提交申诉
export const addAppeal = data => {
  return http.request("post", `${baseURL.api}/web/appeal/submit`, { data });
};

//提交申诉
export const checkAppeal = data => {
  return http.request("post", `${baseURL.api}/web/appeal/done`, { data });
};

//申诉详情
export const appealDetail = params => {
  return http.request("get", `${baseURL.api}/web/appeal/detail`, {
    params
  });
};

//后台 - 删除待举证的申诉单
export const delAppeal = params => {
  return http.request("delete", `${baseURL.api}/web/appeal/${params.id}`, {
    params
  });
};

//直接获取中台订单详情
export const orderDetail = params => {
  return http.request("get", `${baseURL.api}/web/customer/raw/order/detail`, {
    params
  });
};

//下载海报
export const download = params => {
  return http.request("get", `${baseURL.api}/web/promotion/poster/download`, {
    params
  });
};

//获取图片
export const getPic = (data: string) => {
  return http.request<string>(
    "get",
    `${baseURL.api}/web/crm/voucher?object=${data}`
  );
};

//删除图片
export const delPic = data => {
  return http.request("delete", `${baseURL.api}/web/delete/object/${data}`);
};

//上传图片
export const upload = (data: FormData) => {
  return http.request<string>(
    "post",
    `${baseURL.api}/web/upload/object`,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//根据InfoUUID获取外呼机器人对话明细
export const robotTaskDetail = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/robotTaskDetail/dialogue/${id}`
  );
};

//追溯
export const history = data => {
  return http.request("post", `${baseURL.api}/web/transfer/trace`, { data });
};

//转移订单
export const transferOrder = data => {
  return http.request("post", `${baseURL.api}/web/order/transfer`, { data });
};

//  转移线索
export const transferCustom = data => {
  return http.request("post", `${baseURL.api}/web/customer/transfer`, { data });
};

//订单查询
export const orderList = data => {
  return http.request("post", `${baseURL.api}/web/order/list`, { data });
};

//订单数据导入
export const orderImport = data => {
  return http.request("post", `${baseURL.api}/web/order/qimo/migrate`, {
    data
  });
};

//订单数据---上传
export const orderUpload = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/order/qimo/upload`,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};
