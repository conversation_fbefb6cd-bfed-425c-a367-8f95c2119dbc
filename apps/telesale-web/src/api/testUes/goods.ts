/*
 * @Date         : 2024-07-18 16:18:04
 * @Description  : goods相关的api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface GoodsReq {
  remark: string;
  goodsID: string;
  name: string;
}

// 获取列表
export const getGoodListApi = params => {
  return http.request<{
    list: any[];
    goods: any[];
    total: number;
  }>("get", `${baseURL.api}/sync-order/goods/listTestGoods`, {
    params
  });
};

// 获取商品详情
export const getGoodInfoApi = params => {
  return http.request("get", `${baseURL.api}/sync-order/goods/getTestGoods`, {
    params
  });
};

// 新增商品
export const addGoodApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/sync-order/goods/createTestGoods`,
    {
      data
    }
  );
};

// 编辑商品
export const updateGoodApi = data => {
  return http.request(
    "put",
    `${baseURL.api}/sync-order/goods/updateTestGoods`,
    {
      data
    }
  );
};

// 删除商品
export const delGoodApi = params => {
  return http.request(
    "delete",
    `${baseURL.api}/sync-order/goods/deleteTestGoods`,
    {
      params
    }
  );
};
