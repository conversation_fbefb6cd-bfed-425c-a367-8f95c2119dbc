/*
 * @Date         : 2025-04-25 11:35:56
 * @Description  : 优秀案例库API
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { http } from "../../utils/http";
import baseURL from "../url";

// 标签对象接口
export interface TagInstance {
  id: number;
  name: string;
  key: string;
  groupId: number;
  score: number;
}

// 标签组接口
export interface TagGroupInstance {
  id: number;
  libraryId: number;
  name: string;
  key: string;
  tags: TagInstance[];
  score: number;
}

// 标签查询响应接口
export interface TagQueryReply {
  tags: TagGroupInstance[];
}

/**
 * @description: 获取知识库标签信息
 * @param {number} libraryId 知识库ID
 * @return {Promise<TagQueryReply>} 标签信息
 */
export function getTagQuery(libraryId: number) {
  return http.get<TagQueryReply, any>(
    `${baseURL.aisaas}/web/${libraryId}/tag/query`
  );
}

// 优秀案例列表查询参数接口
export interface ExcellentCasesQueryParams {
  actionId?: string; // 通话ID
  workerId?: string | number; // 坐席ID
  orgId?: string | number; // 组织ID
  beginTime?: number; // 开始时间
  endTime?: number; // 结束时间
  minDuration?: number; // 最小通话时长(秒)
  maxDuration?: number; // 最大通话时长(秒)
  pages?: number; // 页码
  pageSize?: number; // 每页数量
  callRating?: string; // 通话评级
  reinspectionResult?: string; // 复检结果
}

// 优秀案例列表项接口
export interface ExcellentCaseItem {
  id: number; // 案例ID
  actionId: string; // 通话ID
  questionCount: number; // 问题数量
  addedQuestionCount: number; // 已添加问题数量
  createdAt: number; // 创建时间
  workerId?: string | number; // 坐席ID
  workerName?: string; // 坐席名称
  callRating?: string; // 通话评级
  reinspectionResult?: string; // 复检结果
  duration?: number; // 通话时长
}

// 优秀案例列表响应接口
export interface ExcellentCasesQueryReply {
  list: ExcellentCaseItem[];
  total: number;
}

// 优秀案例更新接口
export interface ExcellentCaseUpdateParams {
  id: number; // 案例ID
  actionId: string; // 通话ID
  questions?: string[]; // 问题列表
  tags?: number[]; // 标签ID列表
  callRating?: string; // 通话评级
  reinspectionResult?: string; // 复检结果
  reviewComments?: string; // 复核意见
}

// 对话消息接口
export interface ConversationMessage {
  role: "user" | "assistant"; // 消息角色：用户或助手
  content: string; // 消息内容
  start?: string; // 开始时间戳
  time?: number; // 消息时间
}

// 案例对话文本响应接口
export interface ExcellentCaseTextReply {
  text: {
    id: string;
    actionId: string;
    createdAt: string;
    updatedAt: string;
    texts: RoleTextInstance[];
    status: string;
    infoUUID: string;
    userId: string;
    audioURL: string;
    taskId: string;
  };
}

export interface RoleTextInstance {
  role: "user" | "assistant";
  content: string;
  start?: string;
  time?: number;
}

/**
 * @description: 获取优秀案例列表
 * @param {ExcellentCasesQueryParams} params 查询参数
 * @return {Promise<ExcellentCasesQueryReply>} 优秀案例列表
 */
export function getExcellentCasesList(params: ExcellentCasesQueryParams) {
  return http.get<any, ExcellentCasesQueryReply>(
    `${baseURL.robot}/admin/excellentCase`,
    { params }
  );
}

/**
 * @description: 更新优秀案例
 * @param {ExcellentCaseUpdateParams} params 更新参数
 * @return {Promise<any>} 更新结果
 */
export function updateExcellentCase(params: ExcellentCaseUpdateParams) {
  return http.post(`${baseURL.robot}/admin/excellentCase`, { data: params });
}

/**
 * @description: 获取优秀案例对话文本
 * @param {string} actionId 通话ID
 * @return {Promise<ExcellentCaseTextReply>} 对话文本信息
 */
export function getExcellentCaseText(actionId: string) {
  return http.get<any, ExcellentCaseTextReply>(
    `${baseURL.robot}/admin/excellentCaseText/${actionId}`
  );
}

/** 机器人/人工评级信息 */
export interface CaseAppraiseInstance {
  score?: string; // 评级的分数
  result?: string; // 评级的结果
  createdAt?: string; // 评级时间
  reason?: string; // 原因
}

/** 优秀案例详情接口返回类型（info对象） */
export interface ExcellentCaseDetailReply {
  id: string; // 案例ID
  workerId?: number; // 坐席ID
  workerName?: string; // 坐席名称
  infoUUID?: string; // 线索ID
  status?: string; // 状态
  actionId?: string; // 通话ID
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  begin?: string; // 通话开始时间
  end?: string; // 通话结束时间
  aiAppraise?: CaseAppraiseInstance; // 机器人评级
  humanAppraise?: CaseAppraiseInstance; // 人工评级
  triggerOrderID?: string; // 触发的成单ID
  userId?: string; // 用户ID
  aiAppraiseStatus?: string; // 机器人评级成功状态 fail success process
  audioURL?: string; // 音频地址
  ph?: string; // ph
  CallTimeLength?: number; // 通话时长
}

/**
 * @description: 查询优秀案例详情
 * @param {string|number} id 案例ID
 * @return {Promise<{ info: ExcellentCaseDetailReply }>} 案例详情
 */
export function getExcellentCaseDetail(id: string | number) {
  return http.get<any, { info: ExcellentCaseDetailReply }>(
    `${baseURL.robot}/admin/excellentCase/${id}`
  );
}

// 重试下载录音请求接口
export interface ExcellentCaseTextRetryRequest {
  actionIds: string[]; // 通话ID列表
}

/**
 * @description: 重试下载录音
 * @param {ExcellentCaseTextRetryRequest} params 重试参数，包含通话ID列表
 * @return {Promise<any>} 重试结果
 */
export function retryExcellentCaseAudio(params: ExcellentCaseTextRetryRequest) {
  return http.post(`${baseURL.robot}/admin/excellentCaseText/retry`, {
    data: params
  });
}
