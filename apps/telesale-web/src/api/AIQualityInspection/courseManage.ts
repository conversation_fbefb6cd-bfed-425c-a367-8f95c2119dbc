/*
 * @Date         : 2025-05-14 14:16:06
 * @Description  : 训练课程管理API
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { http } from "../../utils/http";
import baseURL from "../url";

// 训练课程参考问答实例接口
export interface TrainingCourseReferenceQAInstance {
  trainingCourseId?: number; // 课程ID
  referenceQaId?: string; // 词条ID
  score?: number; // 分数
}

// 训练课程实例接口
export interface TrainingCourseInstance {
  id?: number; // 课程ID
  name?: string; // 课程名称
  maxDuration?: number; // 最大持续时间
  botName?: string; // 机器人名称
  botRole?: string; // 机器人角色
  backgroundDesc?: string; // 背景描述
  conversationReq?: string; // 对话要求
  target?: string; // 目标
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  referenceQas?: TrainingCourseReferenceQAInstance[]; // 参考问答列表
}

// 训练课程列表查询参数接口
export interface TrainingCourseQueryParams {
  pages?: number; // 页码（从1开始）
  pageSize?: number; // 每页数量
  nameKeyword?: string; // 课程名称关键词筛选（可选）
  beginTime?: number; // 开始时间
  endTime?: number; // 结束时间
}

// 训练课程列表响应接口
export interface TrainingCourseListReply {
  courses: TrainingCourseInstance[]; // 课程列表
  total: string; // 总记录数
}

// 训练课程创建请求接口
export interface TrainingCourseCreateRequest {
  name?: string; // 课程名称
  maxDuration?: number; // 最大持续时间
  botName?: string; // 机器人名称
  botRole?: string; // 机器人角色
  backgroundDesc?: string; // 背景描述
  conversationReq?: string; // 对话要求
  target?: string; // 目标
  referenceQas?: TrainingCourseReferenceQAInstance[]; // 参考问答列表
}

// 训练课程创建响应接口
export interface TrainingCourseCreateReply {
  id?: number; // 创建的课程ID
}

// 训练课程详情响应接口
export interface TrainingCourseInfoReply {
  course: TrainingCourseInstance; // 课程详情
}

// 训练课程更新响应接口
export interface TrainingCourseUpdateReply {
  success?: boolean; // 更新是否成功
}

/**
 * @description: 获取训练课程列表
 * @param {TrainingCourseQueryParams} params 查询参数
 * @return {Promise<TrainingCourseListReply>} 训练课程列表
 */
export function getTrainingCourseList(params: TrainingCourseQueryParams) {
  return http.get<any, TrainingCourseListReply>(
    `${baseURL.robot}/admin/trainingCourse`,
    { params }
  );
}

/**
 * @description: 创建训练课程
 * @param {TrainingCourseCreateRequest} data 课程数据
 * @return {Promise<TrainingCourseCreateReply>} 创建结果
 */
export function createTrainingCourse(data: TrainingCourseCreateRequest) {
  return http.request<TrainingCourseCreateReply>(
    "put",
    `${baseURL.robot}/admin/trainingCourse`,
    {
      data
    }
  );
}

/**
 * @description: 获取训练课程详情
 * @param {number} id 课程ID
 * @return {Promise<TrainingCourseInfoReply>} 课程详情
 */
export function getTrainingCourseInfo(id: number) {
  return http.get<any, TrainingCourseInfoReply>(
    `${baseURL.robot}/admin/trainingCourse/${id}`
  );
}

/**
 * @description: 更新训练课程
 * @param {number} id 课程ID
 * @param {Partial<TrainingCourseInstance>} data 更新数据
 * @return {Promise<TrainingCourseUpdateReply>} 更新结果
 */
export function updateTrainingCourse(
  id: number,
  data: Partial<TrainingCourseInstance>
) {
  return http.post(`${baseURL.robot}/admin/trainingCourse/${id}`, { data });
}
