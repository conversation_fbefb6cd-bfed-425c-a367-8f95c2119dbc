import { http } from "../utils/http";
import baseURL from "./url";
import QS from "qs";

//获取转介绍活动列表
export const getListTransfer = params => {
  return http.request("get", `${baseURL.api}/web/promotion`, {
    params
  });
};

//添加转介绍活动
export const addTransfer = data => {
  return http.request("post", `${baseURL.api}/web/promotion`, {
    data
  });
};

//更新转介绍活动
export const editTransfer = data => {
  return http.request("put", `${baseURL.api}/web/promotion`, {
    data
  });
};

//转介绍活动详情
export const detailTransfer = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/promotion/detail/${params.id}`,
    {
      params
    }
  );
};

//上下架
export const updateStatus = data => {
  return http.request("put", `${baseURL.api}/web/promotion/status`, {
    data
  });
};

//下载海报
export const downloadTransfer = params => {
  return http.request("get", `${baseURL.api}/web/promotion/poster/download`, {
    params
  });
};

//判断用户是否有转介绍的资格
export const isDownload = params => {
  return http.request<{
    quality: boolean;
    promotionId: number;
    posterPath: string;
    name: string;
  }>("get", `${baseURL.api}/web/promotion/poster/quality`, {
    params
  });
};

//获取小程序可用列表
export const getListWeChat = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/find`
  );
};

//获取渠道活码列表
export const getChannelList = params => {
  return http.request("get", `${baseURL.api}/wuhan-miniprogram/qrcode`, {
    params
  });
};

//上下线
export const updateStatusChannel = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/qrcode/upDownShelf`,
    {
      data
    }
  );
};

//添加渠道活码
export const addChannel = data => {
  return http.request("post", `${baseURL.api}/web/qrcode`, {
    data
  });
};

//更新渠道活码
export const editChannel = data => {
  return http.request("put", `${baseURL.api}/web/qrcode`, {
    data
  });
};

//渠道活码详情
export const detailChannel = id => {
  return http.request("get", `${baseURL.api}/web/qrcode/${id}`);
};

//渠道活码详情-根据坐席 ID 聚合查询坐席业绩等信息
export const getAgentsMsg = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/qrcodeWorkerPerformance/aggregation`,
    {
      params,
      paramsSerializer: function (params) {
        return QS.stringify(params, { indices: false });
      }
    }
  );
};

//获取企业微信tag
export const getTag = (params?: { accountId: number }) => {
  return http.request(
    "get",
    `${baseURL.api}/web/admin/setting/channel_qr/tag`,
    { params }
  );
};

//全量规则模板列表（仅有名称和ID）
export const getAllRuleConfig = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate/enum`
  );
};

//获取规则模板列表
export const getListRuleConfig = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate`,
    {
      params
    }
  );
};

//创建渠道活码规则模板
export const addRuleConfig = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate`,
    {
      data
    }
  );
};

//更新渠道活码规则模板信息
export const editRuleConfig = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate/${data.id}`,
    {
      data
    }
  );
};

//根据ID查询规则模板详情
export const detailRuleConfig = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate/take/${id}`
  );
};

//根据ID删除渠道活码规则模板
export const deleteRuleConfig = id => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-miniprogram/qrCodeRuleTemplate/${id}`
  );
};

//渠道活码ui配置列表
export const getListConfig = params => {
  return http.request("get", `${baseURL.api}/wuhan-miniprogram/configuration`, {
    params
  });
};

//创建渠道活码ui配置
export const createConfig = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/configuration`,
    {
      data
    }
  );
};
//更新渠道活码ui配置
export const updateConfig = data => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/configuration`, {
    data
  });
};

//获取渠道活码ui配置详情
export const detailConfig = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/configuration/${id}`
  );
};

//删除渠道活码ui配置
export const deleteConfig = id => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-miniprogram/configuration/${id}`
  );
};

//视频号视频素材列表
export const getListVideo = params => {
  return http.request("get", `${baseURL.api}/wuhan-miniprogram/video/list`, {
    params
  });
};

//添加视频号视频素材
export const addVideo = data => {
  return http.request("post", `${baseURL.api}/wuhan-miniprogram/video`, {
    data
  });
};

//更新视频号视频素材
export const editVideo = data => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/video`, {
    data
  });
};

//视频号视频素材详情
export const detailVideo = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/video/detail/${params.id}`,
    {
      params
    }
  );
};

//视频号视频素材上下架
export const updateStatusVideo = data => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/video/status`, {
    data
  });
};

//删除视频号视频素材
export const deleteVideo = id => {
  return http.request("delete", `${baseURL.api}/wuhan-miniprogram/video/${id}`);
};

//视频号预约素材列表
export const getListSchedule = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/list`,
    {
      params
    }
  );
};

//添加视频号预约频素材
export const addSchedule = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/live/schedule`,
    {
      data
    }
  );
};

//更新视频号预约素材
export const editSchedule = data => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/live/schedule`, {
    data
  });
};

//视频号预约素材详情
export const detailSchedule = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/detail/${params.id}`,
    {
      params
    }
  );
};

//删除视频号预约素材
export const deleteSchedule = id => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/${id}`
  );
};

interface CommonSetting {
  name: string;
  value: any;
  note: string;
}
// 获取资料管理
export const getProfileApi = params => {
  return http.request<CommonSetting>(
    "get",
    `${baseURL.api}/wuhan-worker/settings`,
    {
      params
    }
  );
};

// 保存资料管理
export const setProfileApi = (data: CommonSetting) => {
  return http.request("post", `${baseURL.api}/wuhan-worker/settings`, {
    data
  });
};

//暖暖素材列表
export const getListWarm = params => {
  return http.request("get", `${baseURL.api}/customer-service/material`, {
    params
  });
};

//添加暖暖素材
export const addWarm = data => {
  return http.request("post", `${baseURL.api}/customer-service/material`, {
    data
  });
};

//更新暖暖素材
export const editWarm = data => {
  return http.request("put", `${baseURL.api}/customer-service/material`, {
    data
  });
};

//暖暖素材详情
export const detailWarm = params => {
  return http.request(
    "get",
    `${baseURL.api}/customer-service/material/take/${params.id}`,
    {
      params
    }
  );
};

//暖暖素材上下架
export const updateStatusWarm = data => {
  return http.request(
    "put",
    `${baseURL.api}/customer-service/material/upDown`,
    {
      data
    }
  );
};

//暖暖设置默认素材
export const defaultWarm = data => {
  return http.request(
    "put",
    `${baseURL.api}/customer-service/material/default`,
    {
      data
    }
  );
};

//无分页分类列表
export const getListClassify = () => {
  return http.request("get", `${baseURL.api}/customer-service/tag/find`);
};

//添加暖暖素材分类
export const addClassify = data => {
  return http.request("post", `${baseURL.api}/customer-service/tag`, {
    data
  });
};

//更新暖暖素材分类
export const editClassify = data => {
  return http.request("put", `${baseURL.api}/customer-service/tag`, {
    data
  });
};

//拖拽暖暖素材分类
export const dragClassify = data => {
  return http.request("put", `${baseURL.api}/customer-service/tag/drag`, {
    data
  });
};

//删除暖暖素材分类
export const delClassify = id => {
  return http.request("delete", `${baseURL.api}/customer-service/tag/${id}`);
};

// 获取直播主题列表
export const getLiveThemeApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/configuration/list`,
    {
      params
    }
  );
};

// 创建直播主题列表
export const createLiveThemeApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/live/configuration`,
    {
      data
    }
  );
};

// 更新直播主题列表
export const updateLiveThemeApi = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/live/configuration`,
    {
      data
    }
  );
};

// 上架主题
export const setThemeUpApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/live/configuration/up`,
    {
      data
    }
  );
};

// 获取主题详情
export const getThemeInfoApi = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/configuration`,
    {
      params: {
        id
      }
    }
  );
};

// 获取直播预约列表
export const getLiveScheduleApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/list`,
    {
      params
    }
  );
};

// 创建直播预约列表
export const createLiveScheduleApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/live/schedule`,
    {
      data
    }
  );
};

// 更新直播预约列表
export const updateLiveScheduleApi = data => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/live/schedule`, {
    data
  });
};

// 获取直播预约详情
export const getScheduleInfoApi = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/detail/${id}`
  );
};

// 删除直播预约
export const deleteScheduleApi = id => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-miniprogram/live/schedule/${id}`
  );
};
