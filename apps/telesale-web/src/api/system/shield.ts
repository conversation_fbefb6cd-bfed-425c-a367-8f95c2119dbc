/*
 * @Date         : 2024-02-20 15:43:48
 * @Description  : 屏蔽地区api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";
import { AddShieldAddress, ShieldAddressList } from "/@/types/system/shield";

/**
 * @description: 获取屏蔽地区列表
 * @type: 1：区域，2：学校
 * @returns {ShieldAddressList}
 */
export const getShieldListApi = ({ type }: { type: number }) => {
  return http.request<ShieldAddressList[]>(
    "get",
    `${baseURL.api}/web/block_rule/list/${type}`
  );
};

/**
 * @description: 获取屏蔽地区详情
 * @returns {ShieldAddressList}
 */
export const getShieldInfoApi = (id: number) => {
  return http.request<ShieldAddressList>(
    "get",
    `${baseURL.api}/web/block_rule/detail/${id}`
  );
};

/**
 * @description: 新增屏蔽地区
 * @param {AddShieldAddress} data
 */
export const AddShieldAddressApi = (data: AddShieldAddress) => {
  return http.request("post", `${baseURL.api}/web/block_rule/add`, { data });
};

/**
 * @description: 编辑屏蔽地区
 * @param {AddShieldAddress} data
 */
export const updateShieldAddressApi = (data: AddShieldAddress) => {
  return http.request("post", `${baseURL.api}/web/block_rule/update`, { data });
};
