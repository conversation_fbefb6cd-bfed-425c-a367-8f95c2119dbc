/*
 * @Date         : 2024-03-19 14:07:40
 * @Description  : 选择坐席页面的api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import baseURL from "../url";
import { http } from "/@/utils/http";

export interface BelongingCueQuery {
  isEffective: boolean;
  defaultWorker?: number;
  notify?: WorkerList[];
}

export interface WorkerList {
  orgId: number;
  name: string;
  workerId: number;
}

/**
 * @description: 导入坐席
 */
export const importWorkerApi = (data: FormData) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/importPushWorker`,
    {
      data
    }
  );
};
