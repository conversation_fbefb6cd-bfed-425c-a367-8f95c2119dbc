/*
 * @Date         : 2024-11-15 12:27:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../..//utils/http";
import baseURL from "../url";

interface ExperimentGroup {
  enable: boolean;
  workers: number[];
  rates: Rate[];
  allocateThreshold: number;
}

interface Rate {
  tag: string;
  rate: number;
}

// 获取商业化实验组配置
export const getExperimentGroupApi = () => {
  return http.request<ExperimentGroup>(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/getExperimentGroup`
  );
};

// 是否是实验组坐席
export const isExperimentGroupWorkerApi = (params: { workerId: number }) => {
  return http.request<{
    isExperimentGroupWorker: boolean;
  }>("get", `${baseURL.api}/wuhan-datapool/admin/isExperimentGroupWorker`, {
    params
  });
};

/**
 * @description: 保存商业化实验组配置
 * @param {ExperimentGroup} data
 */
export const saveExperimentGroupApi = (data: ExperimentGroup) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/saveExperimentGroup`,
    { data }
  );
};

/**
 * @description: 商业化实验组数据统计
 * @param {number} duration
 * @param {string} tag
 */
export const getExperimentGroupStatisticsApi = (params: {
  duration: number;
  tag: string;
}) => {
  return http.request<{
    leadsCount: number;
    remainCount: number;
    tagLeads: number[];
    tagRemain: number[];
  }>("get", `${baseURL.api}/wuhan-datapool/admin/statisticsExperimentGroup`, {
    params
  });
};

export interface SelfServicePoolReqQuery {
  /**
   * 学段
   */
  stage?: string;
}

export interface SelfServicePool {
  stage?: string;
  dailyLimit?: number;
  dailyPayLimit?: number;
}

/**
 * @description 获取自助公海池配置
 * https://yapi.yc345.tv/project/2352/interface/api/119106
 * <AUTHOR>
 * @date 2025-01-13
 * @export
 * @param {SelfServicePoolReqQuery} params
 * @returns {Promise<SelfServicePoolResBody>}
 */
export const getSelfServicePool = (params: SelfServicePoolReqQuery) => {
  return http.request<SelfServicePool>(
    `get`,
    `${baseURL.api}/wuhan-datapool/admin/getSelfServicePool`,
    {
      params
    }
  );
};

/**
 * @description 保存自助公海池配置
 * https://yapi.yc345.tv/project/2352/interface/api/119218
 * <AUTHOR>
 * @date 2025-01-13
 * @export
 * @param {SelfServicePool} data
 */
export const saveSelfServicePool = (data: SelfServicePool) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-datapool/admin/saveSelfServicePool`,
    {
      data
    }
  );
};

export interface SelfServicePoolAllocateReqQuery {
  groupId?: number;
}

export interface SelfServicePoolAllocateResBody {
  list?: {
    groupId?: number;
    name?: string;
    dailyAllocate?: number;
    paidLimit?: number;
  }[];
}

/**
 * @description 获取自助公海池小组分配量
 * https://yapi.yc345.tv/project/2352/interface/api/124430
 * <AUTHOR>
 * @date 2025-04-01
 * @export
 * @param {SelfServicePoolAllocateReqQuery} params
 * @returns {Promise<SelfServicePoolAllocateResBody>}
 */
export const getSelfServicePoolGroupApi = (
  params: SelfServicePoolAllocateReqQuery
) => {
  return http.request<SelfServicePoolAllocateResBody>(
    `get`,
    `${baseURL.api}/wuhan-datapool/admin/listSelfServicePoolAllocate`,
    {
      params
    }
  );
};

/**
 * @description 保存自助公海池小组分配量
 * https://yapi.yc345.tv/project/2352/interface/api/124486
 * <AUTHOR>
 * @date 2025-04-01
 * @export
 * @param {SelfServicePoolAllocateResBody} data
 */
export const saveSelfServicePoolGroupApi = (
  data: SelfServicePoolAllocateResBody
) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-datapool/admin/saveSelfServicePoolAllocate`,
    {
      data
    }
  );
};

export interface ExcludesGroupSettingsReq {
  groupIds?: number[];
}

/**
 * @description 保存不能领取公海池线索的小
 * https://yapi.yc345.tv/project/2352/interface/api/124614
 * <AUTHOR>
 * @date 2025-04-01
 * @export
 * @param {ExcludesGroupSettingsReq} data
 */
export const saveExcludesGroupSettingsApi = (
  data: ExcludesGroupSettingsReq
) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-datapool/admin/saveDisabledGroupSettings`,
    {
      data
    }
  );
};

/**
 * @description 获取不能领取公海池线索的小组
 * https://yapi.yc345.tv/project/2352/interface/api/124542
 * <AUTHOR>
 * @date 2025-04-01
 * @export
 * @returns {Promise<ExcludesGroupSettingsReq>}
 */
export const getExcludesGroupSettingsApi = () => {
  return http.request<ExcludesGroupSettingsReq>(
    `get`,
    `${baseURL.api}/wuhan-datapool/admin/getDisabledGroupSettings`
  );
};
