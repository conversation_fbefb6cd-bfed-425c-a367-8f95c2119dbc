/*
 * @Date         : 2024-07-04 16:50:31
 * @Description  : 坐席选择设置接口
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";

/**
 * @description: 通用获取坐席选择配置
 * @returns {}
 */
export const getWokerSelectApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/admin/push/worker`,
    {
      params
    }
  );
};

/**
 * @description: 通用保存坐席选择配置
 * @returns {}
 */
export const setWokerSelectApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/admin/push/worker`,
    {
      data
    }
  );
};
