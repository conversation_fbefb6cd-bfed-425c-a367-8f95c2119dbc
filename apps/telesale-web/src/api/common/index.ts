/*
 * @Date         : 2024-05-09 14:10:47
 * @Description  : 共用apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * @description: 上传永久文件api，不会过期的
 * @param {*} data
 */
export const uploadPermanentFileApi = (data: FormData) => {
  return http.request<string>(
    "post",
    `${baseURL.api}/wuhan-datapool/file/upload`,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * @description: 转发接口
 * @param {string} target 目标服务
 * @param {string} targetPath 目标路径
 */
export const forwardApi = (params: {
  target?: string;
  host?: string;
  targetPath: string;
  method?: "get" | "post";
  [x: string]: any;
}) => {
  return http.request<any>(
    params.method || "get",
    `${baseURL.api}/web/third_party/proxy`,
    {
      params,
      data: params
    },
    {
      hideError: params.hideError
    }
  );
};

interface TeamInfo {
  teamId: number;
  name: string;
}

/**
 * @description: 获取所有服务期团队
 * @returns {TeamInfo}
 */
export const getTeamListApi = () => {
  return http.request<{
    teams: TeamInfo[];
  }>("get", `${baseURL.api}/wuhan-datapool/info/getUserAllocationTeam`);
};
