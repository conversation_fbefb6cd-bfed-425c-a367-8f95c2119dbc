import { http } from "../utils/http";
import baseURL from "./url";
import { useUserStoreHook } from "/@/store/modules/user";
const pureUser = useUserStoreHook();

//本地直接通过第三方接口获取token和用戶信息
export const getUserInfoWithMail = params => {
  return http.request(
    "get",
    `${baseURL.gearboxDomain}/auth/loginForDevelopment`,
    {
      params
    }
  );
};

//发送埋点
export const sendPoint = data => {
  return http.request("post", `${baseURL.point}`, { data });
};

//注册token
export const registToken = () => {
  return http.request("get", `${baseURL.api}/web/regist/token`);
};

//获取当前坐席信息
export const getPersonMsg = () => {
  return http.request("get", `${baseURL.api}/web/profile/load`);
};

//修改当前坐席信息
export const setPersonMsg = data => {
  return http.request("post", `${baseURL.api}/web/profile/save`, { data });
};

//生成员工头像
export const creatUserAvatar = data => {
  return http.request("post", `${baseURL.api}/wuhan-worker/worker/avatar`, {
    data
  });
};

//关联企业微信账号
export const bindWechat = (params: { accountId: number }) => {
  return http.request(
    "get",
    `${baseURL.api}/web/worker/bind/qrcode`,
    { params },
    { responseType: "arraybuffer" }
  );
};

//根据任务Id，查询后台异步任务
export const task = params => {
  return http.request("get", `${baseURL.api}/web/sync/task/status`, {
    params
  });
};

function paramsHandle(params: any = {}) {
  if (!params.siteId) {
    params.siteId = pureUser.tenantBelongSite.id;
  } else if (params.siteId === -1) {
    delete params.siteId;
  }
}

//列出所有坐席
export const findAllAgent = (params: any = {}) => {
  paramsHandle(params);
  return http.request("get", `${baseURL.api}/web/organization/worker/all`, {
    params
  });
};

//查询组织架构
export const findOrganization = (params = {}) => {
  paramsHandle(params);
  return http.request("get", `${baseURL.api}/web/organization/list`, {
    params
  });
};

//列出指定坐席所管理的坐席
export const findAgent = () => {
  return http.request("get", `${baseURL.api}/web/organization/worker/manage`);
};

//查询指定坐席可见的组织架构
export const findReferOrg = () => {
  return http.request("get", `${baseURL.api}/web/organization/manage/list`);
};

//查看坐席历史分配指标
export const metric = () => {
  return http.request("get", `${baseURL.api}/web/worker/history/metric`);
};

//查询洋葱目前科目列表
export const findSubject = () => {
  return http.request("get", `${baseURL.api}/web/subject/list`);
};

//公海池--指定查询
export const findPool = data => {
  return http.request("post", `${baseURL.api}/web/customer/pool/analy`, {
    data
  });
};

//重洋启思登录-验证码
export const getTestCode = params => {
  return http.request("get", `${baseURL.api}/wuhan-worker/token/verifyCode`, {
    params
  });
};
//重洋启思-登录
export const login = data => {
  return http.request("post", `${baseURL.api}/wuhan-worker/token/login`, {
    data
  });
};

//Token-用户信息
export const getTenantMsg = () => {
  return http.request("get", `${baseURL.api}/wuhan-worker/token/me`);
};

//全量站点信息
export const getSiteList = () => {
  return http.request<{
    list: IdNameOptions;
  }>("get", `${baseURL.api}/wuhan-worker/site`);
};

/**
 * @description: 是否是分期支付实验组坐席
 */
export const getIsInstallmentApi = (workerId: number) => {
  return http.request<{
    isInstallment: boolean;
  }>("post", `${baseURL.api}/wuhan-worker/worker/isInstallment`, {
    params: { workerId: workerId }
  });
};
