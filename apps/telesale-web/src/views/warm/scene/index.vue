<script lang="ts" setup>
import { ref, nextTick, onMounted } from "vue";
import { useAppStore } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { getSceneListApi, getChannelListApi } from "/@/api/warm";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import RePagination from "/@/components/RePagination/index.vue";
import AddOrUpdate from "./dialog/AddOrUpdate.vue";
import Search from "./components/Search.vue";
import baseURL from "/@/api/url";
import { ElMessage } from "element-plus";
import { getListWarm } from "/@/api/active";

const { device } = useAppStore();
const { authorizationMap } = useUserStoreHook();
const loading = ref<boolean>(false);
const isModal = ref<boolean>(false);
const sceneId = ref<number | undefined>(undefined);
const total = ref(0);
const formRefs = ref<InstanceType<typeof Search> | null>();
const channelList = ref<any[]>([]);
const audioList = ref<any[]>([]);

//分页
const rePaginationRefs = ref<InstanceType<typeof RePagination> | null>();
function onSearch() {
  rePaginationRefs.value?.onSearch();
}

const dataList = ref<any[]>();

const listHeader = [
  {
    field: "id",
    desc: "场景ID"
  },
  {
    field: "name",
    desc: "场景名称"
  },
  {
    field: "channelId",
    desc: "指定渠道",
    filters: row => getValue(row, "channelId", channelList.value)
  },
  {
    field: "materialId",
    desc: "默认音频",
    filters: row => getValue(row, "materialId", audioList.value)
  },
  {
    field: "createdAt",
    desc: "创建时间",
    timeChange: 3
  }
];

const operation = [
  {
    event: "edit",
    text: "编辑",
    isShow: authorizationMap.includes("telesale_admin_warm_scene_set")
  },
  {
    event: "copyLink",
    text: "复制链接"
  }
];

const getValue = (row, key, arr) => {
  return arr.find(item => item.id === row[key])?.name ?? "";
};
const getList = () => {
  const params = {
    ...formRefs.value?.form,
    pageIndex: rePaginationRefs.value.pageIndex,
    pageSize: rePaginationRefs.value.pageSize
  };
  console.log("params-", params);
  loading.value = true;
  getSceneListApi(params)
    .then(({ data }: { data: any }) => {
      dataList.value = data.list;
      total.value = data.total;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
};

const openModal = (id?: number) => {
  sceneId.value = id;
  nextTick(() => {
    isModal.value = true;
  });
};

const getSelectAll = () => {
  getChannelListApi().then(({ data }: { data: any }) => {
    channelList.value = data.list;
  });
  getListWarm({ upShelfState: 2 }).then(({ data }: { data: any }) => {
    audioList.value = data.list;
    audioList.value.unshift({
      id: 0,
      name: "默认"
    });
  });
};

const copy = params => {
  console.log("copy", params);
  let link = `${baseURL.shareH5}/heatingSupport.html?scene=${params.id}&channelId=${params.channelId}`;
  const input = document.createElement("input");
  input.setAttribute("value", link);
  document.body.appendChild(input);
  input.select();
  document.execCommand("copy") && ElMessage.success("复制成功！");
  document.body.removeChild(input);
};

const parantMath = ({ key, params }) => {
  switch (key) {
    case "edit":
      openModal(params.id);
      break;

    case "copyLink":
      copy(params);
      break;

    default:
      break;
  }
};

onMounted(() => {
  onSearch();
  getSelectAll();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div class="g-table-box">
        <Search
          ref="formRefs"
          :channelList="channelList"
          @onSearch="onSearch"
          @addScene="openModal()"
          v-model:loading="loading"
        />
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :width-operation="200"
          :operation="operation"
          @parantMath="parantMath"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
        />
      </div>
      <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
      <AddOrUpdate
        v-model:value="isModal"
        :id="sceneId"
        :channelList="channelList"
        :audioList="audioList"
        v-if="isModal"
        @on-search="getList"
      />
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.g-add-btn {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
</style>
