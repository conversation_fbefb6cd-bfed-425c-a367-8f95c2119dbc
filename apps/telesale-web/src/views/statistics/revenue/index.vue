<script setup lang="ts" name="revenue">
import { ref, onMounted } from "vue";
import { getRevenue } from "/@/api/statistics";
import paramsHandle from "/@/utils/handle/paramsHandle";

import Search from "./components/Search.vue";

//带分页列表数据必备
let loading = ref(true);
let revenueData = ref({
  amount: "",
  avgPrice: "",
  orderNum: "",
  stage: "",
  avgUserPrice: "",
  paidUserNum: "",
  avgFamilyPrice: "",
  paidFamilyNum: ""
});

//form查询
const formRefs = ref();
function getList() {
  loading.value = true;
  let params = paramsHandle(formRefs.value.form, {
    time: true,
    string: ["workerid"],
    zero: ["department", "syncType"]
  });
  params.workerid && params.orgIdList && delete params.orgIdList;

  getRevenue(params)
    .then(({ data }: { data: any }) => {
      revenueData.value = data;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <Search ref="formRefs" @onSearch="getList" />
      <div class="d-grid-cont">
        <el-row :gutter="20">
          <el-col :sm="8">
            <div class="g-grid-content topLeft">
              <div class="titleName">营收总额/元</div>
              <div class="titleNum">
                <span>{{ revenueData.amount }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content topRight">
              <div class="titleName">订单量</div>
              <div class="titleNum">
                <span>{{ revenueData.orderNum }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content center">
              <div class="titleName">件均价/元</div>
              <div class="titleNum">
                <span>{{ revenueData.avgPrice }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content center">
              <div class="titleName">付款用户数</div>
              <div class="titleNum">
                <span>{{ revenueData.paidUserNum }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content center">
              <div class="titleName">均客单价/元</div>
              <div class="titleNum">
                <span>{{ revenueData.avgUserPrice }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content center">
              <div class="titleName">付款家庭数</div>
              <div class="titleNum">
                <span>{{ revenueData.paidFamilyNum }}</span>
              </div>
            </div>
          </el-col>
          <el-col :sm="8">
            <div class="g-grid-content center">
              <div class="titleName">均家庭客单价/元</div>
              <div class="titleNum">
                <span>{{ revenueData.avgFamilyPrice }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>
<style scoped lang="scss">
.d-grid-cont {
  padding: 50px;
  background: #fff;
}
</style>
