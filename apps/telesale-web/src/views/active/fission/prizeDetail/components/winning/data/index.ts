/*
 * @Date         : 2025-02-27 11:33:03
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { storeToRefs } from "pinia";
import { TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";
import { getLabel } from "@telesale/shared";
import { goodsTypeList } from "/@/utils/data/goodsTypeList";

const { allAgentObj } = storeToRefs(useUserStore());

export const columns: TableColumns[] = [
  {
    field: "onionId",
    desc: "洋葱ID"
  },
  {
    field: "mobile",
    desc: "手机号"
  },
  {
    field: "rewardType",
    desc: "奖品类型",
    filterOptions: {
      columns: goodsTypeList
    },
    customRender: ({ text }) => {
      return getLabel(text, goodsTypeList);
    }
  },
  {
    field: "rewardName",
    desc: "奖品名称"
  },
  {
    field: "rewardAt",
    desc: "中奖时间",
    timeChange: 2
  },
  {
    field: "deliverGoodsStatus",
    desc: "发放状态"
  },
  {
    field: "activityName",
    desc: "活动名称"
  },
  {
    field: "activityTime",
    desc: "活动时间"
  }
];
