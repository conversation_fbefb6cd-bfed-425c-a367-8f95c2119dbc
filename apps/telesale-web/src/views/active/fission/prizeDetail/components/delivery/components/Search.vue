<!--
 * @Date         : 2024-07-17 17:29:45
 * @Description  : 查询组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { DeliveryParams } from "/@/api/active/delivery";

interface Emits {
  (e: "onSearch"): void;
  (e: "reset"): void;
  (e: "update:form", val: DeliveryParams): void;
}

const props = defineProps<{
  form: DeliveryParams;
}>();

const emits = defineEmits<Emits>();

function onSearch() {
  emits("onSearch");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: DeliveryParams) {
    emits("update:form", val);
  }
});

const reset = () => {
  form.value = {};
  emits("reset");
};
</script>
<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="mobile">
      <el-input
        v-model="form.mobile"
        placeholder="请输入客户手机号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="rewardName">
      <el-input
        v-model="form.rewardName"
        placeholder="请输入奖品名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="rewardTime">
      <sync-date-picker
        v-model:value="form.rewardTime"
        dateRange="after"
        type="datetimerange"
        value-format="x"
        range-separator="至"
        start-placeholder="中奖时间-开始"
        end-placeholder="中奖时间-结束"
        :default-time="[
          new Date(2000, 1, 1, 0, 0, 0),
          new Date(2000, 2, 1, 23, 59, 59)
        ]"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="reset">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>
