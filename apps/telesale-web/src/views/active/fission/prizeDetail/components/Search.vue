<!--
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动中奖明细查询组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import {
  FissionPrizeDetailReq,
  prizeStatusOptions
} from "/@/api/active/fission";
import { FormInstance } from "element-plus";

interface Emits {
  (e: "onSearch"): void;
  (e: "onReset"): void;
  (e: "update:form", val: FissionPrizeDetailReq): void;
}

const props = defineProps<{
  form: FissionPrizeDetailReq;
}>();

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();

function onSearch() {
  emit("onSearch");
}

function onReset() {
  if (formRef.value) {
    formRef.value.resetFields();
    // 重置时间范围
    form.value.prizeTime = [];
    // 重置奖品状态
    form.value.prizeStatus = undefined;
  }
  emit("onReset");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: FissionPrizeDetailReq) {
    emit("update:form", val);
  }
});
</script>

<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="onionId">
      <el-input
        v-model="form.onionId"
        placeholder="洋葱ID"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="phone">
      <el-input
        v-model="form.phone"
        placeholder="手机号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="prizeTime">
      <SyncDatePicker
        v-model:value="form.prizeTime"
        type="datetimerange"
        value-format="x"
        range-separator="至"
        start-placeholder="中奖时间-开始"
        end-placeholder="中奖时间-结束"
        :default-time="[
          new Date(2000, 1, 1, 0, 0, 0),
          new Date(2000, 2, 1, 23, 59, 59)
        ]"
      />
    </el-form-item>
    <el-form-item prop="prizeStatus">
      <el-select v-model="form.prizeStatus" placeholder="奖品状态" clearable>
        <el-option
          v-for="item in prizeStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="onReset">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>
