<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup name="Delivery">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { listHeader } from "./data";
import { OperationObj } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./components/Search.vue";
import SetDelivery from "./dialog/SetDelivery.vue";
import {
  exportOrderApi,
  getDeliveyListApi,
  uploadOrderApi
} from "/@/api/active/delivery";
import { downloadFile } from "@telesale/shared";
import { formatTime } from "/@/utils/common";
import UploadDataOrder from "./dialog/UploadDataOrder.vue";
import { cloneDeep } from "lodash-es";

const rowData = ref();
const isModal = ref<boolean>(false);
const uploadModal = ref<boolean>(false);
const tableRefs = ref<InstanceType<typeof ReTable>>();
const uploadData = ref();
const { dataList, Pagination, onSearch, searchForm, loading } = useTable({
  api: getDeliveyListApi,
  beforeRequest: data => {
    const res = cloneDeep(data);
    formatTime(res, "rewardAtStart", "rewardAtEnd", "rewardTime");
    return res;
  }
});

const operation: OperationObj[] = [
  {
    text: "设置物流信息",
    eventFn: row => {
      isModal.value = true;
      rowData.value = row;
    }
  }
];

const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

function uploadFile(file) {
  let formData = new FormData();
  formData.append("file", file.file);
  loading.value = true;

  uploadOrderApi(formData)
    .then(res => {
      ElMessage.success("上传成功");
      uploadData.value = res.data;
      uploadModal.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
}

const exportOrder = () => {
  loading.value = true;
  const data = cloneDeep(searchForm.value);
  formatTime(data, "rewardAtStart", "rewardAtEnd", "rewardTime");

  exportOrderApi(data)
    .then(res => {
      downloadFile(res);
    })
    .finally(() => {
      loading.value = false;
    });
};

const reset = () => {
  tableRefs.value?.clearSort();
  tableRefs.value?.resetFilter();
  onSearch();
};
</script>

<template>
  <div v-loading="loading">
    <div class="flex justify-between">
      <Search
        ref="formRefs"
        v-model:form="searchForm"
        @onSearch="onSearch"
        @reset="reset"
      />
    </div>
    <div class="flex justify-end mb-10px gap-10px">
      <el-upload
        action="#"
        :http-request="uploadFile"
        :auto-upload="true"
        :show-file-list="false"
        accept=".xlsx"
        v-auth="'telesale_admin_delivery_import_export'"
      >
        <el-button type="primary">上传</el-button>
      </el-upload>
      <el-button
        v-auth="'telesale_admin_delivery_import_export'"
        type="primary"
        @click="exportOrder"
      >
        导出
      </el-button>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        :width-operation="130"
        @filter-head-data="filterHeadData"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
    </div>
    <Pagination />
    <SetDelivery
      v-if="isModal"
      v-model:value="isModal"
      :dataMemory="rowData"
      @onSearch="onSearch"
    />
    <UploadDataOrder
      v-if="uploadModal"
      v-model:value="uploadModal"
      :list="uploadData"
      @onSearch="onSearch"
    />
  </div>
</template>
