<script lang="ts" setup>
import Delivery from "./components/delivery/index.vue";
import Winning from "./components/winning/index.vue";
const active = ref("1");
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="active">
        <el-tab-pane label="中奖明细" name="1">
          <Winning />
        </el-tab-pane>
        <el-tab-pane label="发货管理" name="2" lazy>
          <Delivery />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
