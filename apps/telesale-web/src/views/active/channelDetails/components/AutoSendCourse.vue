<script setup lang="ts">
import { computed } from "vue";

interface Props {
  form: any;
  type: string;
}
const props = defineProps<Props>();

interface Emits {
  (e: "update:form", val: any): void;
}

const emit = defineEmits<Emits>();

const form = computed({
  get() {
    return props.form;
  },
  set(val: any) {
    emit("update:form", val);
  }
});

function changeHome() {
  form.value.courses = [];
  form.value.authDay = "";
}

const propsParams = { multiple: true, emitPath: false };
const courseList = [
  {
    value: "sync",
    label: "同步课",
    children: [
      {
        value: 1,
        label: "小学",
        children: [
          {
            value: "vip#1-1",
            label: "数学"
          },
          {
            value: "vip#1-8",
            label: "自然"
          },
          {
            value: "vip#1-9",
            label: "地球"
          },
          {
            value: "vip#1-10",
            label: "实验"
          },
          {
            value: "vip#1-3",
            label: "语文"
          }
        ]
      },
      {
        value: 2,
        label: "初中",
        children: [
          {
            value: "vip#2-1",
            label: "数学"
          },
          {
            value: "vip#2-5",
            label: "英语"
          },
          {
            value: "vip#2-3",
            label: "语文"
          },
          {
            value: "vip#2-6",
            label: "生物"
          },
          {
            value: "vip#2-2",
            label: "物理"
          },
          {
            value: "vip#2-4",
            label: "化学"
          },
          {
            value: "vip#2-7",
            label: "地理"
          }
        ]
      },
      {
        value: 3,
        label: "高中",
        children: [
          {
            value: "vip#3-1",
            label: "数学"
          },
          {
            value: "vip#3-2",
            label: "物理"
          },
          {
            value: "vip#3-6",
            label: "生物"
          },
          {
            value: "vip#3-4",
            label: "化学"
          },
          {
            value: "vip#3-5",
            label: "英语"
          }
        ]
      }
    ]
  },
  {
    value: "custom",
    label: "培优课",
    children: [
      {
        value: 1,
        label: "小学",
        children: []
      },
      {
        value: 2,
        label: "初中",
        children: []
      },
      {
        value: 3,
        label: "高中",
        children: []
      }
    ]
  }
];
</script>

<template>
  <el-form-item label="自动发放课程" prop="isAuthCourses">
    <el-switch
      @change="changeHome"
      v-model="form.isAuthCourses"
      :disabled="type === 'detail'"
    />
  </el-form-item>
  <el-form-item
    label="科目"
    prop="courses"
    v-if="form.isAuthCourses"
    :rules="{
      required: true,
      message: '请选择科目',
      trigger: 'change'
    }"
  >
    <el-cascader
      v-model="form.courses"
      placeholder="请选择科目"
      separator=""
      :options="courseList"
      :props="propsParams"
      clearable
      :disabled="type === 'detail'"
    />
  </el-form-item>

  <el-form-item
    prop="authDay"
    label="赠送天数"
    v-if="form.isAuthCourses"
    :rules="{
      required: true,
      message: '请选择赠送天数',
      trigger: 'change'
    }"
  >
    <el-select
      v-model="form.authDay"
      placeholder="请选择赠送天数"
      :disabled="type === 'detail'"
    >
      <el-option label="1天" :value="1" />
      <el-option label="2天" :value="2" />
      <el-option label="3天" :value="3" />
      <el-option label="7天" :value="7" />
      <el-option label="10天" :value="10" />
      <el-option label="21天" :value="21" />
      <el-option label="30天" :value="30" />
    </el-select>
  </el-form-item>
</template>
<style scoped lang="scss">
:deep(.el-cascader) {
  width: 100%;
  .el-input {
    width: 100%;
  }
}
</style>
