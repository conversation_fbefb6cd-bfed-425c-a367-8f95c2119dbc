<!--
 * @Date         : 2024-05-29 15:30:30
 * @Description  : 上传资料
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref, computed } from "vue";
import ProfileForm from "../components/ProfileForm.vue";

interface Props {
  value: boolean;
  id?: number;
}

interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModal = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const acitve = ref("1");

const closeModal = () => {
  isModal.value = false;
};

const onSearch = () => {
  closeModal();
  emit("success");
};
</script>

<template>
  <div>
    <el-dialog
      :title="props.id ? '编辑资料' : '新增资料'"
      v-model="isModal"
      :before-close="closeModal"
      width="800px"
    >
      <el-tabs v-model="acitve" v-if="!props.id">
        <el-tab-pane label="单个上传" name="1">
          <ProfileForm @success="onSearch" @close="closeModal" />
        </el-tab-pane>
        <el-tab-pane label="批量上传" name="2" lazy>
          <ProfileForm
            :multiple="true"
            @success="onSearch"
            @close="closeModal"
          />
        </el-tab-pane>
      </el-tabs>
      <ProfileForm
        v-else
        :id="props.id"
        @success="onSearch"
        @close="closeModal"
      />
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input) {
  width: 300px;
}
</style>
