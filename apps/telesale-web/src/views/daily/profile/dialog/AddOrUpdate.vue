<!--
 * @Date         : 2024-05-29 15:30:30
 * @Description  : 上传资料
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref, computed } from "vue";
import ProfileForm from "../components/ProfileForm.vue";

interface Props {
  value: boolean;
  id?: number;
}

interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const loading = ref(false);
const singleForm = ref<InstanceType<typeof ProfileForm>>();
const batchForm = ref<InstanceType<typeof ProfileForm>>();
const updateForm = ref<InstanceType<typeof ProfileForm>>();

const isModal = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const acitve = ref("1");

const closeModal = () => {
  isModal.value = false;
};

const submitForm = () => {
  if (props.id) {
    updateForm.value?.submitForm();
    return;
  }

  if (acitve.value === "1") {
    singleForm.value?.submitForm();
  } else {
    batchForm.value?.submitForm();
  }
};

const onSearch = () => {
  closeModal();
  emit("success");
};
</script>

<template>
  <div>
    <el-dialog
      :title="props.id ? '编辑资料' : '新增资料'"
      v-model="isModal"
      :before-close="closeModal"
      width="800px"
    >
      <el-tabs v-model="acitve" v-if="!props.id">
        <el-tab-pane label="单个上传" name="1">
          <ProfileForm
            ref="singleForm"
            v-model:loading="loading"
            @success="onSearch"
            @close="closeModal"
          />
        </el-tab-pane>
        <el-tab-pane label="批量上传" name="2" lazy>
          <ProfileForm
            ref="batchForm"
            v-model:loading="loading"
            :multiple="true"
            @success="onSearch"
            @close="closeModal"
          />
        </el-tab-pane>
      </el-tabs>
      <ProfileForm
        v-else
        ref="updateForm"
        v-model:loading="loading"
        :id="props.id"
        @success="onSearch"
        @close="closeModal"
      />
      <template #footer>
        <el-button :loading="loading" type="primary" @click="submitForm">
          确定
        </el-button>
        <el-button :loading="loading" @click="closeModal">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input) {
  width: 300px;
}
:deep(.el-dialog__body) {
  display: flex;
  .el-tabs__content {
    overflow: scroll;
  }
  .el-tabs {
    display: flex;
    flex-direction: column;
  }
}
</style>
