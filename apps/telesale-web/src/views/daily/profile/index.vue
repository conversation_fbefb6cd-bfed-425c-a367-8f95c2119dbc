<!--
 * @Date         : 2024-05-09 12:27:35
 * @Description  : 专属资料
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup name="Profile">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import { listHeader, getOperation } from "./data";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import AddorUpdate from "./dialog/AddOrUpdate.vue";
import {
  getProfileApi,
  deleteProfileApi,
  batchDeleteProfileApi
} from "/@/api/daily/profile";
import Search from "./components/Search.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const { device } = storeToRefs(useAppStore());

const { loading, dataList, searchForm, onSearch, Pagination } = useTable({
  api: getProfileApi
});

const rowId = ref<number | undefined>();
const isModal = ref<boolean>(false);
const tableRefs = ref();

const openModal = (id?: number) => {
  rowId.value = id || undefined;
  isModal.value = true;
};

// 批量删除
const batchDelete = () => {
  const ids = tableRefs.value?.handleSelectionChange();
  if (ids.length === 0) {
    ElMessage.warning("请先选择要删除的资料");
    return;
  }

  ElMessageBox.confirm(`确认是否删除相关资料？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const idList = ids.map(item => item.id);
    batchDeleteProfileApi(idList).then(() => {
      ElMessage.success("批量删除成功");
      onSearch();
    });
  });
};

const operation = getOperation(openModal, onSearch);
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div>
        <Search ref="formRefs" v-model:form="searchForm" @onSearch="onSearch" />
      </div>
      <div class="mb-20px flex justify-end">
        <el-button type="danger" @click="batchDelete()">批量删除</el-button>
        <el-button type="primary" @click="openModal()">新增</el-button>
      </div>
      <div>
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :width-operation="150"
          :operation="operation"
          :selection="true"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
        />
      </div>
      <Pagination class="mt-10px" />
      <AddorUpdate
        v-if="isModal"
        v-model:value="isModal"
        :id="rowId"
        @success="onSearch"
      />
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
