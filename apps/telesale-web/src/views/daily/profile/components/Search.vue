<!--
 * @Date         : 2024-07-17 17:29:45
 * @Description  : 查询组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { goodTypeOptions, stageList, subjectList } from "../data";
import { ProfileReq } from "/@/api/daily/profile";

interface Emits {
  (e: "onSearch"): void;
  (e: "update:form", val: ProfileReq): void;
}

const props = defineProps<{
  form: ProfileReq;
}>();

const emit = defineEmits<Emits>();

function onSearch() {
  emit("onSearch");
}

const formRef = ref();

const form = computed({
  get() {
    return props.form;
  },
  set(val: ProfileReq) {
    emit("update:form", val);
  }
});

const resetForm = () => {
  formRef.value.resetFields();
  onSearch();
};
</script>
<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="materialName">
      <el-input
        v-model="form.materialName"
        placeholder="请输入资料名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="belongGood">
      <el-select
        v-model="form.belongGood"
        placeholder="请选择所属商品"
        clearable
        multiple
        filterable
        collapse-tags
        collapse-tags-tooltip
      >
        <el-option
          v-for="item in goodTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="stage">
      <el-select
        v-model="form.stage"
        placeholder="请选择学段"
        multiple
        clearable
        filterable
        collapse-tags
        collapse-tags-tooltip
      >
        <el-option
          v-for="item in stageList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="subject">
      <el-select
        v-model="form.subject"
        placeholder="请选择学课"
        multiple
        clearable
        filterable
        collapse-tags
        collapse-tags-tooltip
      >
        <el-option
          v-for="item in subjectList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="resetForm">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>
