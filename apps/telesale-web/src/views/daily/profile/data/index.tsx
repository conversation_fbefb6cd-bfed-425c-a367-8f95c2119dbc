/*
 * @Date         : 2024-03-27 14:36:11
 * @Description  :资料管理
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ElMessage, ElMessageBox } from "element-plus";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import { deleteProfileApi } from "/@/api/daily/profile";

// 商品类型选项
export const goodTypeOptions = [
  { label: "不限", value: 1 },
  { label: "998一年积木块", value: 2 },
  { label: "组合商品", value: 3 },
  { label: "升单商品", value: 4 },
  { label: "体验机尾款", value: 5 },
  { label: "大会员商品", value: 6 },
  { label: "其他指定商品", value: 7 }
];

export const stageList = [
  { label: "不限", value: 1 },
  { label: "小学", value: 2 },
  { label: "初中", value: 3 },
  { label: "高中", value: 4 }
];
// 不限/数学/语文/英语/自然/地球/实验/物理/化学/生物/地理
export const subjectList = [
  { label: "不限", value: 1 },
  { label: "数学", value: 2 },
  { label: "语文", value: 3 },
  { label: "英语", value: 4 },
  { label: "自然", value: 5 },
  { label: "地球", value: 6 },
  { label: "实验", value: 7 },
  { label: "物理", value: 8 },
  { label: "化学", value: 9 },
  { label: "生物", value: 10 },
  { label: "地理", value: 11 }
];

// key与学段value一致，value为subjectList的value数组
export const subjectMap = {
  2: [1, 2, 3, 4, 5, 6, 7], // 小学
  3: [1, 2, 3, 4, 8, 9, 10, 11], // 初中
  4: [1, 2, 3, 4, 8, 9, 10] // 高中
};

export const listHeader: TableColumns[] = [
  {
    field: "id",
    desc: "ID",
    minWidth: 60
  },
  {
    field: "materialName",
    desc: "资料名称"
  },
  {
    field: "belongGood",
    desc: "所属商品"
  },
  {
    field: "stage",
    desc: "学段"
  },
  {
    field: "subject",
    desc: "学科"
  },
  {
    field: "createdAt",
    desc: "创建时间"
  }
];

export const getOperation = (
  openModal: (id: number) => void,
  onSearch: () => void
): OperationObj[] => {
  return [
    {
      text: "编辑",
      eventFn: params => {
        openModal(params.id);
      }
    },
    {
      text: "删除",
      eventFn: params => {
        ElMessageBox.confirm("是否删除该数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          deleteProfileApi(params.id).then(() => {
            ElMessage.success("删除成功");
            onSearch();
          });
        });
      }
    }
  ];
};
