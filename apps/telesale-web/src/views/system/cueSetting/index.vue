<!--
 * @Date         : 2024-02-20 18:04:24
 * @Description  : 线索操作设置
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import { getAuth } from "/@/utils/auth";
import CueLock from "./components/CueLock.vue";
import ManualEntry from "./components/ManualEntry.vue";
import WeComMax from "./components/WeComMax.vue";

const active = ref<string>("1");
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="active">
        <el-tab-pane
          v-if="getAuth('telesale_admin_system_cueSetting_lockTab')"
          label="锁定线索上限"
          name="1"
        >
          <CueLock />
        </el-tab-pane>
        <el-tab-pane
          v-if="getAuth('telesale_admin_system_cueSetting_manualEntry')"
          label="人工录入线索上限"
          name="2"
          lazy
        >
          <ManualEntry />
        </el-tab-pane>
        <el-tab-pane
          v-if="getAuth('telesale_admin_system_cueSetting_weComMax')"
          label="企微备注入库上限"
          name="3"
          lazy
        >
          <WeComMax />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
