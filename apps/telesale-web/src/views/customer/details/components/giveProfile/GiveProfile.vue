<!--
 * @Date         : 2024-05-09 17:46:05
 * @Description  : 赠送资料
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { ProfileInfo, getProfileApi } from "/@/api/daily/profile";
import { GiveProfile, giveProfileApi } from "/@/api/customer/details";
import {
  goodTypeOptions,
  stageList,
  subjectList,
  subjectMap
} from "/@/views/daily/profile/data";

interface Props {
  userId: string;
  source?: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance>();
const profileList = ref<ProfileInfo[]>();
const filteredProfileList = ref<ProfileInfo[]>();
const form = ref<GiveProfile>({
  userId: props.userId,
  ids: []
});

// 筛选条件
const filterForm = ref({
  belongGood: 1, // 默认不限
  stage: 1, // 默认不限
  subject: undefined // 默认不限
});

// 当前可选的学科列表
const currentSubjectList = computed(() => {
  if (filterForm.value.stage === 1) {
    // 如果学段是"不限"，则显示所有学科
    return subjectList;
  } else {
    // 根据选择的学段筛选学科
    const subjectIds = subjectMap[filterForm.value.stage];
    return subjectList.filter(item => subjectIds?.includes(item.value));
  }
});

const rules: FormRules = {
  ids: [
    {
      required: true,
      message: "请选择要赠送的资料",
      trigger: "change"
    }
  ]
};

const changeGood = () => {
  getProfileList();
};

const changeStage = (e: number) => {
  filterForm.value.subject = undefined;
  getProfileList();
};

const getProfileList = () => {
  const params = {
    belongGood: filterForm.value.belongGood
      ? [filterForm.value.belongGood]
      : undefined,
    stage: filterForm.value.stage ? [filterForm.value.stage] : undefined,
    subject: filterForm.value.subject ? [filterForm.value.subject] : undefined,
    pageSize: 1000,
    pageIndex: 1
  };
  loading.value = true;
  getProfileApi(params)
    .then(res => {
      profileList.value = res.data.list;
      filteredProfileList.value = [...profileList.value];
    })
    .finally(() => {
      loading.value = false;
    });
};

// 筛选资料列表
getProfileList();

const handleClose = () => {
  emit("success");
};

const submitForm = () => {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      giveProfileApi({ ...form.value, source: props.source })
        .then(res => {
          if (res.data.failNum > 0) {
            ElMessage.error(
              `发送成功${res.data.successNum}个，失败${res.data.failNum}个`
            );
          } else {
            ElMessage.success(`发送成功${res.data.successNum}个`);
          }
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <div>
    <div class="d-tip-box">
      <IconifyIconOffline icon="information-line" style="font-size: 18px" />
      <span>
        专属资料将被发送至APP-我的-消息中心-通知中，查看资料需要用户的 APP版本在
        7.59.0 及以上。
      </span>
    </div>
    <el-form
      :model="form"
      label-suffix="："
      ref="ruleFormRef"
      v-loading="loading"
      :rules="rules"
      label-position="top"
    >
      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-form-item
          label="请选择赠送的资料归属商品"
          required
          label-position="top"
        >
          <el-radio-group v-model="filterForm.belongGood" @change="changeGood">
            <el-radio
              v-for="item in goodTypeOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="请选择赠送的资料学段"
          required
          label-position="top"
        >
          <el-radio-group v-model="filterForm.stage" @change="changeStage">
            <el-radio
              v-for="item in stageList"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="请选择赠送的资料学科"
          required
          label-position="top"
          v-if="filterForm.stage !== 1"
        >
          <el-radio-group v-model="filterForm.subject" @change="getProfileList">
            <el-radio
              v-for="item in currentSubjectList"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <el-form-item prop="ids">
        <template #label>
          <span class="d-label">请选择要赠送的资料：</span>
        </template>
        <el-select
          ref="selectRef"
          v-model="form.ids"
          placeholder="请选择资料"
          clearable
          filterable
          style="width: 100%"
          multiple
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="5"
        >
          <el-option
            v-for="item in filteredProfileList"
            :key="item.id"
            :label="item.materialName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <div class="el-dialog__footer">
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="loading"
          :loading="loading"
        >
          确定
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.d-cont {
  > div {
    color: #3b7cd5;
    padding-bottom: 10px;
    font-size: 18px;
  }

  p {
    padding-bottom: 10px;

    span:first-child {
      font-weight: bold;
      color: #666;
    }
  }
}

.d-tip-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;

  span {
    margin-left: 10px;
    color: #666;
  }
}

.d-label {
  font-weight: bold;
  color: #666;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-bottom: 15px;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #666;
}
</style>
