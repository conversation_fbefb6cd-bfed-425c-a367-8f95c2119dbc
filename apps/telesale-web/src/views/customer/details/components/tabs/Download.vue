<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-27 16:34:00
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-26 17:14:34
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/views/customer/details/components/tabs/Download.vue
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<script lang="ts" setup>
import { ref } from "vue";
import { download } from "/@/api/order";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "/@/store/modules/user";
import downloadFile from "/@/utils/handle/downloadFile";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";

interface Props {
  dataMemory: any;
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: "customDetails"
});

const loading = ref(false);
const picUrl = ref("");
const tabList = ref([]);
const active = ref();

const getTabs = () => {
  loading.value = true;
  getPlatformListApi()
    .then(res => {
      tabList.value = res.data.list.map((item: any) => {
        return {
          label: item.name,
          name: item.id
        };
      });

      if (props.dataMemory?.picMsg?.promotionId > 0) {
        active.value = tabList.value?.[0]?.name;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

function downloadMath(val = false) {
  loading.value = true;
  let params = {
    promotionId: props.dataMemory?.picMsg?.promotionId,
    workerId:
      props.type === "transferPoster"
        ? useUserStoreHook().userMsg.id
        : props.dataMemory.workerid,
    userId: props.dataMemory.userid,
    platformId: active.value
  };

  download(params)
    .then(({ data }: { data: any }) => {
      if (val) {
        picUrl.value = data.url;
      } else {
        downloadFile(data.url, true);
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

getTabs();
</script>

<template>
  <div class="d-box" v-loading="loading">
    <template v-if="props.dataMemory?.picMsg?.promotionId > 0">
      <el-tabs v-model="active" @tab-change="downloadMath(true)">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :label="item.label"
          :name="item.name"
        >
          <div class="font-bold mb-10px text-16px c-black">
            {{ props.dataMemory.picMsg.name }}
          </div>
          <img style="width: 300px; height: 500px" :src="picUrl" alt="海报" />
          <div class="d-btn-copy">
            <el-button
              type="primary"
              :icon="useRenderIcon('download')"
              @click="downloadMath(false)"
            >
              下载海报
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </template>
    <div v-else style="margin: 50px 0">暂无上架的转介绍活动哦！</div>
  </div>
</template>
<style scoped lang="scss">
.d-box {
  text-align: center;
  :deep(.el-tabs__header) {
    position: sticky;
    top: -20px !important;
    left: 0;
    background: #fff;
    z-index: 100;
    margin-top: -20px;
  }
  .d-btn-copy {
    padding-top: 10px;
    > span {
      margin-right: 20px;
    }
  }
}
</style>
