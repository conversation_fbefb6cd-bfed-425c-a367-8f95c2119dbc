<script setup lang="ts">
import { ref, reactive, onActivated, watch, nextTick, markRaw } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";
import { useAppStore } from "/@/store/modules/app";
import { storeToRefs } from "pinia";
import DiffFind from "/@/components/DiffFind/index.vue";
import OrderList from "./tabs/OrderList.vue";
import AuthList from "./tabs/AuthList.vue";
import VedioList from "./tabs/VedioList.vue";
import HistoryList from "./tabs/HistoryList.vue";
import PictureReviewRecord from "./tabs/PictureReviewRecord.vue";
import Download from "./tabs/Download.vue";
import CourseDetail from "./tabs/CourseDetail.vue";
import DocumentaryRecord from "./tabs/DocumentaryRecord.vue";
import CallRecord from "/@/components/CallRecord/index.vue";
import DiffPrice from "/@/businessComponents/DiffPirce/index.vue";
import SetOtherMsg from "./tabs/SetOtherMsg.vue";
import Tool from "./tabs/Tool.vue";
import CustomerOrder from "./tabs/CustomerOrder.vue";
import AiPlan from "./tabs/AiPlan.vue";
import AddPad from "./tabs/AddPad.vue";
import Report from "./tabs/report.vue";
import { isDownload } from "/@/api/active";
import { getAuth } from "/@/utils/auth";
import { useMultiTagsStore } from "/@/store/modules/multiTags";
import Renew from "./tabs/Renew.vue";
import TabsContainer from "./tabs/components/TabsContainer.vue";
import type { TabItem } from "./tabs/composables/useTabsConfig";

interface Props {
  customMsg: any;
  hasRisk: boolean;
}

const props = defineProps<Props>();
const { hasTabName } = storeToRefs(useMultiTagsStore());

const tabName = ref("orderRecord");

const tabsContainerRef = ref();

const dataMemory = ref();
const isHave = ref(false);
function isDownloadMath() {
  isDownload({ userId: props.customMsg.userid }).then(
    ({ data }: { data: any }) => {
      if (data.quality) {
        dataMemory.value = {
          picMsg: data,
          userid: props.customMsg.userid,
          workerid: props.customMsg.workerid
        };
        isHave.value = true;
      } else {
        tabsContainerRef.value.loadTabsConfig();
      }
    }
  );
}

props.customMsg.userid && isDownloadMath();

const allTabs = ref<TabItem[]>([
  {
    name: "setOtherMsg",
    label: "完善信息",
    component: markRaw(SetOtherMsg),
    props: { infoUuid: props.customMsg?.userid || "" },
    show: true
  },
  {
    name: "contentLimit",
    label: "内容权限",
    component: markRaw(AuthList),
    props: { userid: props.customMsg?.userid || "" },
    show: true
  },
  {
    name: "orderRecord",
    label: "订单记录",
    component: markRaw(OrderList),
    props: { userid: props.customMsg?.userid || "" },
    show: true,
    fixed: true
  },
  {
    name: "lookRecord",
    label: "观看记录",
    component: markRaw(VedioList),
    props: { customMsg: props.customMsg },
    show: true
  },
  {
    name: "documentaryRecord",
    label: "跟单记录",
    component: markRaw(DocumentaryRecord),
    props: { infoUuid: props.customMsg?.infoUuid || "", type: 1 },
    show: true
  },
  {
    name: "newDiffPrice",
    label: "补差价查询（新）",
    component: markRaw(DiffPrice),
    props: { userId: props.customMsg?.userid || "" },
    condition: () => getAuth("telesale_admin_diff_price"),
    show: true
  },
  {
    name: "repurchase",
    label: "续购",
    component: markRaw(Renew),
    props: {
      userid: props.customMsg?.userid || "",
      isShowOperation: true,
      type: "repurchase"
    },
    show: true,
    condition: () => getAuth("telesale_admin_repurchase")
  },
  {
    name: "addPad",
    label: "加购平板",
    component: markRaw(AddPad),
    props: { userId: props.customMsg?.userid || "" },
    show: true,
    condition: () => getAuth("telesale_admin_customer_addPad")
  },
  {
    name: "callRecord",
    label: "呼叫记录",
    component: markRaw(CallRecord),
    props: {
      method: "agent",
      fromCall: true,
      phone: props.customMsg.phone || "",
      workerId: props.customMsg.workerid || 0,
      infoUuid: props.customMsg?.infoUuid
    },
    show: true
  },
  {
    name: "history",
    label: "线索追溯",
    component: markRaw(HistoryList),
    props: {
      infoUuid: props.customMsg?.infoUuid || "",
      type: "customer"
    },
    show: true
  },
  {
    name: "courseDetail",
    label: "课程详情",
    component: markRaw(CourseDetail),
    props: { extend: props.customMsg?.extend || "{}" },
    show: props.customMsg.source === "social_media"
  },
  {
    name: "learnReport",
    label: "学情报告",
    component: markRaw(Report),
    props: { userId: props.customMsg?.userid || "" },
    show: !!props.customMsg?.userid
  },
  {
    name: "download",
    label: "下载海报",
    component: markRaw(Download),
    props: { dataMemory: dataMemory },
    show: isHave.value,
    condition: () => getAuth("telesale_admin_downloadPoster")
  },
  {
    name: "pictureReviewRecord",
    label: "上传记录",
    component: markRaw(PictureReviewRecord),
    props: { userid: props.customMsg?.userid || "" },
    show: isHave.value,
    condition: () => getAuth("telesale_admin_uploadPicRecode")
  },
  {
    name: "tool",
    label: "操作",
    component: markRaw(Tool),
    props: {
      userid: props.customMsg?.userid || "",
      hasRisk: props.hasRisk
    },
    show: !!props.customMsg?.userid
  },
  {
    name: "studyPlan",
    label: "AI定制班",
    component: markRaw(AiPlan),
    props: { userId: props.customMsg?.userid || "" },
    show: true,
    condition: () => getAuth("telesale_admin_customer_studyPlan")
  },
  {
    name: "customerOrder",
    label: "客服工单",
    component: markRaw(CustomerOrder),
    show: true,
    props: { userId: props.customMsg?.userid || "" }
  }
]);

watch(
  () => hasTabName.value,
  n => {
    if (n) {
      nextTick(() => {
        tabName.value = n;
        hasTabName.value = "";
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

function handleTabChange(tabName: string) {
  // 可以在这里添加tab切换的逻辑
}

watch(
  () => hasTabName.value,
  n => {
    if (n) {
      nextTick(() => {
        if (tabsContainerRef.value) {
          tabsContainerRef.value.activeTab = n;
        }
        hasTabName.value = "";
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

// 监听isHave变化，更新相关标签页
watch(
  () => isHave.value,
  newVal => {
    // 更新下载海报和上传记录标签页的显示状态
    const downloadTab = allTabs.value.find(tab => tab.name === "download");
    const pictureReviewTab = allTabs.value.find(
      tab => tab.name === "pictureReviewRecord"
    );

    if (downloadTab)
      downloadTab.show = newVal && getAuth("telesale_admin_downloadPoster");
    if (pictureReviewTab)
      pictureReviewTab.show =
        newVal && getAuth("telesale_admin_uploadPicRecode");

    // 重新加载配置
    if (tabsContainerRef.value) {
      tabsContainerRef.value.loadTabsConfig();
    }
  }
);

defineExpose({
  resetPushRecord: () => {
    const documentaryRef =
      tabsContainerRef.value?.getTabRef("documentaryRecord");
    documentaryRef?.resetPushRecord();
  }
});
</script>

<template>
  <TabsContainer
    ref="tabsContainerRef"
    page-name="customerDetails"
    :all-tabs="allTabs"
    default-active-tab="orderRecord"
    @tab-change="handleTabChange"
  />
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // .el-radio-group {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  //   justify-content: center;
  // }
  // .el-radio:last-child {
  //   margin-right: 32px;
  // }
}
</style>
