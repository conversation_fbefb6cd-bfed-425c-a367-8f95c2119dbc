<script setup lang="ts">
import { ref } from "vue";
import { syncLink, delLink } from "/@/api/customer";
import { ElMessage } from "element-plus";
import AutoCode from "../dialog/AutoCode.vue";

interface Props {
  loading?: boolean;
  index: number;
  row: any;
  limit: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: "update:loading", val: boolean): void;
  (e: "getList"): void;
  (e: "edit", val: number): void;
}

const emit = defineEmits<Emits>();
const isModel = ref<boolean>(false);

//操作
function handleUpdate(row) {
  emit("update:loading", true);
  syncLink({ id: row.courseId })
    .then(() => {
      ElMessage.success("同步成功！");
      emit("getList");
    })
    .catch(() => {
      emit("update:loading", false);
    });
}

function handleDelete(row) {
  emit("update:loading", true);
  delLink({ id: row.courseId })
    .then(() => {
      ElMessage.success("移除成功！");
      emit("getList");
    })
    .catch(() => {
      emit("update:loading", false);
    });
}

const edit = row => {
  emit("edit", row.id);
};
</script>

<template>
  <div>
    <el-button type="primary" link @click="isModel = true"
      >动态二维码</el-button
    >
    <Teleport to="body">
      <AutoCode
        v-if="isModel"
        v-model:value="isModel"
        :id="props.row?.id"
        :info="props.row"
      />
    </Teleport>
  </div>
  <template v-if="props.limit">
    <el-popconfirm
      title="确定同步此课程吗？"
      @confirm="handleUpdate(props.row)"
    >
      <template #reference>
        <el-button link type="primary"> 同步 </el-button>
      </template>
    </el-popconfirm>
    <el-popconfirm
      title="确定移除此课程吗？"
      @confirm="handleDelete(props.row)"
    >
      <template #reference>
        <el-button link type="primary"> 移除 </el-button>
      </template>
    </el-popconfirm>
    <el-button type="primary" link @click="edit(props.row)">编辑</el-button>
  </template>
</template>
<style scoped lang="scss">
.d-cont {
  width: 150px;
  height: 150px;
}
</style>
