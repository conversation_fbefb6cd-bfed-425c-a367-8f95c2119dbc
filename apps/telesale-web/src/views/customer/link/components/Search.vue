<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import statusList from "../utils/statusList";
import { isShowLink } from "@telesale/shared/src/utils/business";
import { useQianYuan } from "/@/hooks/business/useQianYuan";

let device = useAppStoreHook().device;
//权限
let qrCode = ref(
  useUserStoreHook().authorizationMap.indexOf(
    "telesale_admin_exclusive_qrcode"
  ) > -1
);

interface Props {
  limit: boolean;
  tagList: any[];
  tagIds: number[];
}

const props = defineProps<Props>();

interface Emits {
  (e: "update:isModelAdd", val: boolean): void;
  (e: "update:isModelLink", val: boolean): void;
  (e: "update:dynamic", val: boolean): void;
  (e: "onSearch", val: boolean): void;
  (e: "resetFitler"): void;
  (e: "addIpadLink"): void;
  (e: "openCard"): void;
}

const emit = defineEmits<Emits>();

const { loading, hasPermissions, getHasPermissions } = useQianYuan();

getHasPermissions();

function link() {
  emit("update:isModelLink", true);
  emit("update:dynamic", false);
}

function linkActive() {
  emit("update:isModelLink", true);
  emit("update:dynamic", true);
}

function add() {
  emit("update:isModelAdd", true);
}

function addIpadLink() {
  emit("addIpadLink");
}

function openCard() {
  emit("openCard");
}

function onSearch(val = false) {
  if (form.amount === 0) {
    return ElMessage.warning("金额必须大于0");
  }
  emit("onSearch", val);
}
let typeList = ["普通商品", "sku组商品"];

//form查询
const form = reactive({
  name: "",
  status: "",
  courseType: "",
  courseTag: undefined,
  amount: undefined
});

const tagIds = computed(() => props.tagIds);
const formRef = ref<FormInstance>();

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  form.status = "";
  form.courseTag = undefined;
  form.amount = undefined;
  emit("resetFitler");
  onSearch(true);
};

defineExpose({
  form
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
    <el-form-item prop="name">
      <el-input
        v-model="form.name"
        placeholder="请输入课程名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="courseType">
      <el-select
        v-model="form.courseType"
        placeholder="请选择课程类型"
        clearable
      >
        <el-option
          v-for="item in typeList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="courseTag">
      <template v-if="props.tagIds.length > 0">
        <el-select
          v-model="tagIds"
          placeholder="请选择课程标签"
          clearable
          multiple
          disabled
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="item in props.tagList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </template>
      <template v-else>
        <el-select
          v-model="form.courseTag"
          placeholder="请选择课程标签"
          clearable
        >
          <el-option
            v-for="item in props.tagList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </template>
    </el-form-item>
    <el-form-item prop="status" v-if="device === 'mobile'">
      <el-select v-model="form.status" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in statusList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="amount">
      <el-input-number
        v-model="form.amount"
        placeholder="请输入课程金额"
        :min="0"
        :max="99999999"
        clearable
        @keyup.enter="onSearch"
        style="width: 200px"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
        重置
      </el-button>
    </el-form-item>
    <el-form-item class="g-set-button">
      <!-- <el-button
        v-auth="'telesale_admin_exclusiveLink_discountsCard'"
        type="primary"
        @click="openCard"
      >
        省钱卡链接
      </el-button> -->
      <el-button v-if="qrCode" type="primary" @click="link">
        创建会场链接
      </el-button>
      <el-button
        v-if="isShowLink() && !hasPermissions && !loading"
        type="primary"
        @click="linkActive"
      >
        创建动态会场链接
      </el-button>
      <el-button type="primary" @click="add" v-if="props.limit">
        添加课程
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input-number .el-input) {
  width: 200px;
}
</style>
