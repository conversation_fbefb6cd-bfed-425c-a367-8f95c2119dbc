<script lang="ts" setup>
import { computed, ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { createErcCode } from "/@/api/customer";
import { useAppStoreHook } from "/@/store/modules/app";
import { useLinkLists } from "../utils/linkLists";
import { useQrcode } from "/@/hooks/useQrdcode";
import { useDemarcationTime } from "/@/hooks/useDemarcationTime";
import { getLabel, getNewDuration, ipadNoneMap } from "@telesale/shared";
import { GoodsConfig } from "/@/types/customer/goodsConfig";
import { getGoodConfigApi } from "/@/api/customer/details";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import ErCodeDown from "/@/components/ErCodeDown/index.vue";

let { getStageGood } = useLinkLists();

const { getDemarcationTime, isDemarcationTime, currentTime, initDemTime } =
  useDemarcationTime();

interface Props {
  value: boolean;
  dynamic: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const { isStages } = storeToRefs(useUserStore());
const goodList = ref<GoodsConfig[]>([]);
const initTime = ref<number>(0);
let loading = ref<Boolean>(false);
let imgUrl = ref<string>("");
const initLoading = ref<boolean>(true);
let device = useAppStoreHook().device;
// const { qrcodeValue, qrcodeList, qrcodeType } = useQrcode();
const ruleFormRef = ref<FormInstance>();
const ipadTypeList = ref([]);
let form = reactive({
  schoolYear: "",
  duration: undefined,
  from: "telesale",
  exchange: "",
  vipType: "common",
  stage: "",
  goods: undefined,
  isInstallment: 2,
  installmentPayType: ["alipayFq"]
});

const rules = reactive<FormRules>({
  schoolYear: [
    {
      required: true,
      message: "请选择年级",
      trigger: "change"
    }
  ],
  duration: [
    {
      required: true,
      message: "请选择时长",
      trigger: "change"
    }
  ],
  // from: [
  //   {
  //     required: true,
  //     message: "请选择项目组",
  //     trigger: "change"
  //   }
  // ],
  exchange: [
    {
      required: true,
      message: "请选择平板",
      trigger: "change"
    }
  ],
  isInstallment: [
    {
      required: true,
      message: "请选择分期支付",
      trigger: "change"
    }
  ],
  installmentPayType: [
    {
      required: true,
      message: "请选择分期支付方式",
      trigger: "change"
    }
  ]
});

function changeVipType() {
  form.exchange = "";
  form.duration = "";
  form.schoolYear = "";
}

const changeStage = () => {
  form.duration = "";
  form.schoolYear = "";
  form.exchange = "";
  form.goods = undefined;
};

const getGoodsConfig = () => {
  if (goodList.value.length === 0) {
    loading.value = true;
    getGoodConfigApi()
      .then(res => {
        res.data.data.stage.forEach(item => {
          item.goodList.forEach(good => {
            if (item.cname === "high") {
              good.name += `-${good.price}元`;
            } else {
              good.name += `-${good.year}年`;
            }
          });
        });
        goodList.value = res.data.data.stage;
        form.stage = goodList.value?.[0]?.cname || undefined;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const changePad = (e: any) => {
  form.goods.url = e.url;
};

const changeGood = e => {
  e?.addPads?.forEach(item => {
    item.value = item.label;
  });
  ipadNoneMap[0].url = e?.url;
  ipadNoneMap[0].amount = e.price || "";
  ipadTypeList.value = [...ipadNoneMap, ...e.addPads];
  form.exchange = "";
};

const changeInstallment = () => {
  form.installmentPayType = ["alipayFq"];
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      await getSeverTime();
      if (initDemTime.value > initTime.value && isDemarcationTime.value) {
        ElMessage.warning("检测到所提交的选项发生了变化，请刷新页面重新提交");
        formEl.resetFields();
        changeVipType();
        initTime.value = currentTime.value;
        return;
      }
      loading.value = true;
      let params: any = {};
      if (isDemarcationTime.value) {
        params = { ...form };
        const { param = {} } = form.goods;
        params.stage = param?.stage ? param?.stage + "" : "";
        params.goodType = param?.goodType;
        params.schoolYear = param?.schoolYear;
        params.duration = param?.duration;
        params.url = form.goods.url;
        params.dynamic = props.dynamic;
        params.name = form.goods?.name;
        params.courseName = form.goods?.name;
        params.exchange = params.exchange || "";
        Reflect.deleteProperty(params, "goods");
      }
      // qrcodeType.value = form.from;
      params.installmentPayType = getInstallmentPayType(
        params.isInstallment,
        params.installmentPayType
      );
      Reflect.deleteProperty(params, "isInstallment");
      createErcCode(params)
        .then(({ data }: { data: any }) => {
          let blob = new Blob([data], { type: "png" });
          imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
          loading.value = false;
          ElMessage.success("操作成功");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

const getSeverTime = async () => {
  try {
    loading.value = true;
    await getDemarcationTime();
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  await getSeverTime();
  initTime.value = currentTime.value;
  initLoading.value = false;
  getGoodsConfig();
});
</script>

<template>
  <el-dialog title="创建会场链接" v-model="isModel" :before-close="handleClose">
    <div v-if="imgUrl" style="text-align: center; padding-bottom: 10px">
      <ErCodeDown :imgUrl="imgUrl" />
    </div>
    <el-form
      v-else
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '150px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="2" />
        <el-col :lg="20">
          <!-- <el-form-item prop="from" label="项目组">
            <el-select
              v-model="form.from"
              placeholder="请选择项目组"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in qrcodeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <template v-if="!initLoading">
            <template v-if="!isDemarcationTime">
              <el-form-item prop="vipType" label="链接类型">
                <el-radio-group v-model="form.vipType" @change="changeVipType">
                  <el-radio label="common">普通版大会员</el-radio>
                  <el-radio label="high">高考版大会员</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="['common', 'high'].includes(form.vipType)">
                <el-form-item
                  prop="duration"
                  label="时长"
                  style="width: 100%"
                  :rules="{
                    required: true,
                    message: '请选择时长',
                    trigger: 'change'
                  }"
                >
                  <el-select
                    v-model="form.duration"
                    placeholder="请选择时长"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in getNewDuration(form.vipType)"
                      :key="index"
                      :label="item.text"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item
                      v-if="form.vipType === 'high' && form.duration === 'timing'"
                      prop="exchange"
                      label="+1000换购平板S30"
                    >
                      <el-switch v-model="form.exchange" active-value="s30" />
                    </el-form-item> -->
              </template>
            </template>
            <template v-else>
              <el-form-item prop="stage" label="学段">
                <el-radio-group v-model="form.stage" @change="changeStage">
                  <el-radio
                    v-for="item in goodList"
                    :key="item.cname"
                    :label="item.cname"
                  >
                    {{ item.name }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                prop="goods"
                label="商品"
                style="width: 100%"
                :rules="{
                  required: true,
                  message: '请选择商品',
                  trigger: 'change'
                }"
              >
                <el-select
                  v-model="form.goods"
                  placeholder="请选择商品"
                  value-key="name"
                  clearable
                  filterable
                  @change="changeGood"
                >
                  <el-option
                    v-for="(item, index) in getStageGood(goodList, form.stage)"
                    :key="index"
                    :label="item.name"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="exchange" label="平板" v-if="form.goods">
                <el-radio-group v-model="form.exchange">
                  <el-radio
                    v-for="item in ipadTypeList"
                    :key="item.value"
                    :label="item.value"
                    @change="changePad(item)"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="商品售价" v-if="form.exchange">
                {{ getLabel(form.exchange, ipadTypeList, "amount", "value") }}
              </el-form-item>
            </template>
          </template>
          <template v-if="isStages">
            <el-form-item label="分期支付" prop="isInstallment">
              <el-radio-group
                v-model="form.isInstallment"
                @change="changeInstallment"
              >
                <el-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="分期支付方式"
              prop="installmentPayType"
              v-if="form.isInstallment === 1"
            >
              <el-checkbox-group v-model="form.installmentPayType">
                <el-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </template>
        </el-col>
        <el-col :lg="2" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)" v-if="!imgUrl">
        生成二维码
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
