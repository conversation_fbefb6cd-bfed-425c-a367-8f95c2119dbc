<script lang="ts" setup>
import { computed, ref, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { erCode } from "/@/api/customerDetails";
import { useUserStore } from "/@/store/modules/user";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { storeToRefs } from "pinia";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import ErCodeDown from "/@/components/ErCodeDown/index.vue";
import { deviceDetection } from "/@/utils/deviceDetection";

interface Props {
  value: boolean;
  data: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const loading = ref<boolean>(false);
const imgUrl = ref<string>("");
const device = deviceDetection();
const { isStages } = storeToRefs(useUserStore());
const ruleFormRef = ref<FormInstance>();
const form = reactive({
  from: "telesale",
  courseId: "",
  name: "",
  isInstallment: 2,
  installmentPayType: ["alipayFq"]
});

const rules = reactive<FormRules>({
  isInstallment: [
    {
      required: true,
      message: "请选择分期支付",
      trigger: "change"
    }
  ],
  installmentPayType: [
    {
      required: true,
      message: "请选择分期支付方式",
      trigger: "change"
    }
  ]
});

const changeInstallment = () => {
  form.installmentPayType = ["alipayFq"];
};

const submitForm = async (formEl: FormInstance) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      const params = {
        uri: props.data.payPage,
        courseName: props.data.cloneName,
        from: form.from,
        strategyType: "",
        schoolYear: "",
        dynamic: true,
        strategyId: props.data.strategyId,
        installmentPayType: undefined
      };
      params.installmentPayType = getInstallmentPayType(
        form.isInstallment,
        form.installmentPayType
      );
      Reflect.deleteProperty(params, "isInstallment");

      // params.courseName = props.data.cloneName;
      erCode(params)
        .then(({ data }: { data: any }) => {
          let blob = new Blob([data], { type: "png" });
          imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
          loading.value = false;
          loading.value = false;
          ElMessage.success("操作成功");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <el-dialog
    title="生成动态二维码"
    v-model="isModel"
    :before-close="handleClose"
  >
    <div v-if="imgUrl" style="text-align: center; padding-bottom: 10px">
      <ErCodeDown :imgUrl="imgUrl" />
    </div>
    <el-form
      v-else
      :model="form"
      label-suffix="："
      :label-width="!device ? '150px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="4" />
        <el-col :lg="16">
          <el-form-item prop="name" label="商品名称">
            {{ props.data.cloneName }}
          </el-form-item>
          <template v-if="isStages">
            <el-form-item label="分期支付" prop="isInstallment">
              <el-radio-group
                v-model="form.isInstallment"
                @change="changeInstallment"
              >
                <el-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="分期支付方式"
              prop="installmentPayType"
              v-if="form.isInstallment === 1"
            >
              <el-checkbox-group v-model="form.installmentPayType">
                <el-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </template>
        </el-col>
        <el-col :lg="4" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)" v-if="!imgUrl">
        生成二维码
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
