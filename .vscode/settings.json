{
  // "editor.formatOnType": true,
  // "editor.formatOnSave": true,
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "editor.tabSize": 2,
  // "editor.formatOnPaste": true,
  "git.confirmSync": false,
  "workbench.startupEditor": "newUntitledFile",
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "css.lint.propertyIgnoredDueToDisplay": "ignore",
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "files.associations": {
    "editor.snippetSuggestions": "top"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "i18n-ally.localesPaths": ["locales"],
  "typescript.tsdk": "node_modules\\typescript\\lib"
}
